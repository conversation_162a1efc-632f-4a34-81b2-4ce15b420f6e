{"FreezeAmount": "Freeze Amount", "OR": "OR", "aDayAgo": "a day ago", "aMinuteAgo": "a minute ago", "aMonthAgo": "a month ago", "aadhaar": "<PERSON><PERSON><PERSON><PERSON>", "aadhaarCardUnder": "ID/Passport/DL is under verification", "aboutUs": "About Us", "accountInformation": "Account Information", "accountInformationStatus1": "<PERSON><PERSON><PERSON><PERSON> card updated", "accountInformationStatus2": "Wallet password set successfully", "accountInformationStatus3": "Phone number registered successfully", "accountInformationStatus4": "Google code generated successfully", "accountName": "Account name", "accountNumber": "Account Number", "accountTotal": "Account Total", "actualAmount": "Actual Amount:", "actualProfit": "Actual Profit", "adAdminWill": "An admin will verify your information after that you can start using the platform", "addAccountInfo": "Complete your account information", "addAddressWarning": "Only support 6 withdrawal addresses and can not be changed.", "addWithdrawal": "Add <PERSON>drawal Address +", "addWithdrawalAddress": "<PERSON><PERSON> Address", "added": "Added", "additionalAmount": "Additional amount", "additionalFunds": "Additional funds", "additionalInvestment": "Additional Investment", "address": "Address", "addressNameTextController": "Address Name", "addressTextHintText": "Please enter the address name", "addressTextLabelText": "Address Name", "alertCancel": "After confirming the cancellation, you will continue to enjoy the benefits.", "alertMsgAdminVerify": "Your account has been created, Please login again.", "alertProceedToLogin": "Proceed to <PERSON>gin", "alertUnbind": "After confirming the unbinding, the full amount in the contract will be returned to your profit wallet after 30 working days, and you will continue to receive income until first 15 working days.", "alreadyHaveAccount": "Already have an account?  ", "amount": "Amount", "amountExceedsMaximumAllowed": "Amount exceeds maximum allowed", "amountMustBeInUnits": "Amount must be in units of", "amountMustBeMultipleOf": "Amount must be a multiple of", "anHourAgo": "an hour ago", "and": "& ", "appName": "SF India : Smart Crypto", "appUpdateAvailable": "App Update Available", "append": "Append", "appendRejectedAmount": "Append rejected amount", "approvedSuccess": "Your account has been approved", "atLeast8character": "At least 8 characters, must contain numbers, at least one upper case and lower case alphabet and must not contain spaces", "atLeast8characterWithoutUpperCase": "At least 8 characters, must contain numbers, one lower case alphabet and must not contain spaces", "auditing": "Auditing", "authentication": "Authentication", "automaticRenewal": "Automatic Renewal", "available": "Available", "availableBalance": "Available Balance", "availableContracts": "Available Contracts", "availableFrom": "Available From", "availableUntil": "Available Until", "backImageLarge": "Back image size is too large", "bank": "Bank", "bankAccount": "Bank Account", "bankText": "Setup a bank address to withdraw", "benefitRules": "Benefit Rules", "bindIdCard": "Bind ID/Passport/DL", "bindMobile": "Bind Mobile Number", "bonusWallet": "Bonus Wallet", "buildYourProfile": "Build your profits", "buy": "Buy", "buyIt": "Buy it", "buyPosition": "Buy position", "buyPrice": "Buy Price", "buyQuantity": "Buy quantity", "buyingPrice": "Buying price", "buyingTimeNotice": "Please note that after 2:00 PM (ET) each day, buying will no longer be available. Thank you for your understanding and support!", "cancel": "Cancel", "cancelSuccess": "Cancelled successfully", "canceled": "Canceled", "captchaCode": "Captcha Code", "celebrityMentor": "Celebrity Mentor", "certificationCompleted": "Verification is already completed", "change": "Change", "changeAuthCode": "Modify google authentication code", "changeGoogleAuthentication": "Change google authentication code", "changeIdentity": "Change Identity", "changeLoginPassword": "Change Login Password", "changeNumber": "Change mobile number", "changePassword": "Change password", "changePaymentPassword": "Change payment password", "changeWalletPassword": "Change wallet password", "chat": "Cha<PERSON>", "chats": "Chats", "checkBoxSignupError": "Please indicate that you have read and agree to the Terms and Conditions and Privacy Policy", "checkEmail": "Check Email", "checkPhone": "Check Phone", "chooseWallet": "<PERSON><PERSON>", "close": "Close", "code": "Code", "collectionWallet": "Collection Wallet", "commission": "Commission", "community": "Community", "communityWallet": "Community Wallet", "completed": "Completed", "confirm": "Confirm", "confirmDelete": "Confirm Delete", "confirmHintText": "Confirm Password", "confirmInvestment": "Confirm Investment", "confirmPasswordLabel": "Confirm Password", "confirmWalletPassword": "Confirm Wallet Password", "confirmWalletPasswordLabel": "Confirm Wallet Password", "connected": "Connected", "connecting": "Connecting...", "contactSupport": "Contact Support", "continueTxt": "Continue", "contractAnnouncement": "Contract Announcement", "contractList": "Contract List", "contractPrice": "Contract Price", "contractSize": "Contract Size", "contractType": "Contract Type", "contractType1": "conservative", "contractType2": "robust", "contractType3": "radical", "contractedAmount": "Contracted Amount", "contracts": "Contracts", "copied": "<PERSON>pied", "copiedClipboard": "Copied to clipboard", "copy": "Copy", "copyCode": "Copy the following code", "countryDialCode": "+52", "createTime": "Create Time", "crypto": "Crypto", "cumulativeIncome": "Cumulative income", "currentVersion": "Current Version", "customValidatorMsg": " must be more than 0 characters", "customerService": "Customer Service", "customerSupport": "Customer Support", "cycle": "Cycle", "daily": "Daily", "darkMode": "Dark Mode", "datePrivacy": "Effective Date: 27/06/2023", "dateTerms": "Effective Date: 27/06/2023", "day": "day", "days": "days", "daysAgo": "days ago", "daysOfHolding": "Days of holding", "daysSinceUnbind": "Days since unbind", "deleteAddress": "Delete Address", "deleteAddressConfirmation": "Are you sure you want to delete this address?", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "depositTip": "What is a Transaction Hash (TxHash)?", "depositWallet": "<PERSON><PERSON><PERSON><PERSON>", "details": "Details", "didntRecieve": "Didn't receive a code?  ", "directMembers": "Direct Members", "done": "Done", "dontHaveAccount": "Don't have account ?  ", "down": "Down", "download": "Download", "downloadComplete": "Download Complete", "downloadFailed": "Download Failed", "downloadUrlNotAvailable": "Download URL not available", "downloading": "Downloading...", "downloadingUpdate": "Downloading Update...", "drawdown": "Drawdown", "driverLicense": "Driver License", "earningsLastDay": "Earnings last day", "editBank": "Edit Bank Account", "email": "Email", "emailAlready": "Email already registered", "emailError": "You cannot transfer to this email address", "emailHintText": "<PERSON><PERSON>", "emailLabelText": "Email", "emailSupport": "Email Support", "emailSupportDesc": "Send us an email", "emailValidatorMsg": "<PERSON><PERSON> is invalid", "emailVerification": "Email Verification Code", "empty": "No data yet", "emptyAddressEmailMsg": "Enter the Withdraw Addresses", "emptyAddressNameMsg": "Enter the address name", "emptyEmailMsg": "Enter the email", "emptyPasswordMsg": "Enter the password", "emptyStringMsg": "The field can't be empty", "emptyUserNameMsg": "Enter the username", "emptyWalletMsg": "Enter the confirm wallet password", "emptyWalletPasswordMsg": "Enter the wallet password", "endTime": "End Time", "enter4to12characters": "Enter 4 to 12 characters, spaces are not allowed", "enterAmount": "Enter Amount", "enterCaptcha": "Enter the captcha code", "enterCode": "Enter Code", "enterEmailCode": "Enter the email verification code", "enterExactly6Digits": "Enter exactly 6 digits", "enterGoogleCode": "Enter the Google Authentication Code", "enterPassword": "Enter Password", "enterPhone": "Enter the phone number", "enterPhoneNumber": "Enter Phone Number", "enterTheCode": "Enter the code", "enterThePassword": "Enter the passwords", "enterTransactionHash": "Enter Transaction Hash", "enterValidErc": "Enter a valid ERC20 address", "enterValidGoogleCode": "Enter valid Google Code", "enterValidPassword": "Enter a valid password", "enterValidPhone": "Enter a valid mobile number", "enterValidTransactionHash": "Please enter a valid transaction hash", "enterValidTrc": "Enter a valid TRC20 address", "enterValidUsername": "Enter a valid username", "enterVerification": "Enter Verification Code", "enterWalletPassword": "Enter Wallet Password", "enterWithdrawalAmount": "Enter withdrawal amount", "enterYourFriendsEmail": "Enter your friends email", "enter_email": "Enter email", "enter_phone_number": "Enter phone number", "erc20": "ERC 20", "erc20_description": "• Always 66 characters (hex format)\n• Starts with 0x (e.g., 0x59d24d4463a49e993dfe456317fe1b9e62e4b7817a9f44702c4c2139aabfcd2e)", "erc20_title": "✅ ERC20 (ETH/BSC/Polygon, etc.):", "error": "Error", "errorAuth": "Authentication has failed, please login again", "errorMsg": "Oops! Something went wrong. Please try again later.", "expectedProfit": "Expected Profit", "expert": "Expert", "failedToDownloadUpdate": "Failed to download update", "failedToInstallUpdate": "Failed to install update", "failedToLoadCommissionStats": "Failed to load commission stats", "fifteenMin": "15 minutes", "finance": "Finance", "finished": "Finished", "firstGeneration": "First Generation", "fiveMin": "5 minutes", "flat": "Flat", "follow": "Follow", "followPurchaseDetails": "Follow Purchase Details", "followPurchaseDetailsTitle": "Follow-up Purchase Details", "followPurchaseRecords": "Follow Purchase Records", "followUpPeriod": "Purchase Time", "followUpPurchaseAmount": "Follow-up Purchase Amount", "following": "Following", "forceUpdateRequired": "Update Required", "forgotPassword": "Forgot Password?", "frontImageLarge": "Front image size is too large", "fundingThreshold": "Funding Threshold", "fundingWallet": "Funding Wallet", "gainers": "Gaine<PERSON>", "google": "google", "googleAuthCode": "Google Authentication Code", "googleAuthCodeHintText": "Please enter the google code", "googleAuthCodeLabelText": " Google Authentication Code", "googleAuthSubTitle2Text": "Android, IOS - Google Authenticator", "googleAuthSubTitleText": "Download Google Authenticator App", "googleAuthTitleText": "Set up Two factor Authentication", "googleAuthentication": "Google Authentication", "googleCodeShouldBe": "Google code should be 6 characters", "googleCodeUpdatedToast": "Google Code Updated", "handlingFee": "Handling Fee", "handlingFeeMessage": "A handling fee is required each time", "haveReadAndAgreedToThe": "Have read and agreed to the", "helpCenter": "Help Center", "high": "High", "hintPhone": "xxxxxxxxxx", "hintWallet": "0xxxxx00xx0x00xx0x0x0x0x0x0x0x0x0x", "home": "Home", "hotStocks": "Hot Stocks", "hoursAgo": "hours ago", "iAgree": "By signing up, I agree with the ", "iHaveReadAndAgreed": "I have read and agreed", "idCard": "ID Card", "idCardBack": "ID/Passport/DL Back", "idCardConfirmation": "ID/Passport/DL Confirmation", "idCardFront": "ID/Passport/DL Front", "idCardNumber": "ID/Passport/DL Number", "idCardNumberErrorMessage": "Enter a valid ID/Passport/DL Number", "idCardNumberHint": "xxxxxxxxxxxx", "in": "in", "inProcess": "In Process", "incorrectCaptcha": "Incorrect Captcha", "incorrectOtp": "Incorrect OTP", "information": "Information", "initialCapital": "Initial capital", "installPermissionRequired": "Install Permission Required", "installationComplete": "Installation Complete", "installationFailed": "Installation Failed", "installing": "Installing...", "installingUpdate": "Installing Update...", "insufficientAvailableBalance": "Insufficient available balance", "insufficientBalance": "Insufficient balance", "intelligentFollowUpInvestment": "Intelligent Follow-up Investment", "interestAmount": "Interest Amount", "interestRate": "Interest Rate", "invalidAmount": "Invalid amount", "invalidPassword": "Invalid password, Try Again", "invalidPassword2": "Incorrect payment password", "investmentAmount": "Investment Amount", "investmentUnits": "Investment Units", "invitationCode": "Invitation code", "invitationCodeError": "Invitation code is invalid", "invitationCodeSuffix": "?invitationCode=", "invitationDescription": "Invest in the future today! Download the SF India : Smart Crypto now and sign up to get started.", "invitationLinkHintText": "http://supfut.com?invitationcode=abcdefg", "invitationLinkLabelText": "Invitation Link", "jobPosition": "Job Position", "justNow": "just now", "language": "Choose Language", "latestPrice": "Latest Price", "latestTransaction": "Latest\nTransaction\nPrice", "leadingConcept": "Leading Concept", "leadingIndustry": "Leading Industry", "letsStart": "Let's Start", "liveChat": "Live Chat", "liveChatDesc": "Chat with our support team", "lockPeriod": "Lock Period", "lockedBalance": "Locked Balance", "logIn": "Log In", "login": "<PERSON><PERSON>", "loginHintText": "<PERSON><PERSON> Username", "loginLabelText": "User Name", "loginLinkHintText": "http://supfut.com/login", "loginLinkLabelText": "Login Link", "loginLinkSuffix": "/login", "loginPasswordUpdatedToast": "Login Password Updated", "logout": "Log Out", "logoutConfirmation": "Are you sure you want to logout?", "losers": "Losers", "lossesTimes": "Losses Times", "low": "Low", "mainstreamCurrency": "Mainstream\nCurrency", "maintenanceNotice": "Maintenance Notice", "market": "Market", "marketOverview": "Market Overview", "marketUpdates": "Market Updates", "max": "Max", "maxDrawdown": "Max Drawdown", "maximumAmount": "Maximum amount is", "maximumPurchaseAmount": "Maximum Purchase Amount", "maximumWithdrawalAmount": "Maximum withdrawal amount is", "memberInfo": "Member Information", "members": "Members", "mentorCommission": "Mentor Commission", "mentors": "Mentors", "message": "Message", "minimumAmount": "Minimum amount is", "minimumAmountNotMeet": "Minimum amount not meet", "minimumPurchaseAmount": "Minimum Purchase Amount", "minimumWithdrawalAmount": "Minimum withdrawal amount is", "minutesAgo": "minutes ago", "missions": "<PERSON><PERSON>", "month": "Month", "monthly": "Monthly", "monthlyReturn": "Monthly Return", "mustBeMultipleOf": "must be a multiple of", "myContracts": "My Contracts", "name": "Name", "nameValidatorMsg": "Must be more than 0 characters", "networkError": "Network Error", "networkLineLabelText": "Network Line", "newPassword": "New Wallet Password", "newPhoneNumberLabel": "New Phone Number", "newWalletPasswordLabel": "New Wallet Password", "news": "News", "newsUpdates": "News Updates", "next": "Next", "noBalanceAvailable": "No balance available", "noBalanceInformation": "No balance information available", "noDataAvailable": "No data available", "noDetailsAvailable": "No details available", "noGalleryPermission": "Please enable photo permissions in settings to continue", "noInternet": "Please validate your network connection", "noPurchasesYet": "No purchases yet", "noResultsFound": "No results found", "notAvailable": "Not Available", "notification": "Notifications", "numOfContracts": "Number of Contracts", "numberOfTransactions": "Number of Transactions", "numberUpdatedToast": "Phone Number Updated", "numbering": "Numbering", "officeCompany": "Office Company", "ok": "OK", "onBoardScreenSubtitle": "Start investing today and your future will change for the better", "oneClickPurchase": "One Click Purchase", "oneClickSmartInvestmentDescription": "One-click smart investment is highly unified and easy to trade. Analysts will enter the market first in the institutional channel. No operation is required during the one-click smart investment period. Just follow the operation, freeing your hands to avoid forgetting to follow the purchase. Daily profits will be automatically restored.", "oneClickSmartInvestmentInstructions": "One-click Smart Investment Instructions", "oneMin": "1 minute", "open": "Open", "openWallet": "Open Wallet", "optional": "Optional", "or": "OR", "orderDate": "Order Date", "orderNo": "Order No", "otherMembers": "Other Members", "otp": "E-mail verification code", "otpCodeError": "Enter exactly 6 digits", "otp_phone": "SMS verification code", "passport": "Passport", "password": "Password", "passwordHint": "**********", "passwordHintText": "Enter Password", "passwordNotEqual": "Enter the matching password", "passwordUpdatedSuccessfully": "Password updated successfully", "passwordUpdatedToast": "Password Updated", "passwordValidationMsg": "Your password must be at least 8 characters long, contain at least one number and have a mixture of uppercase and lowercase letters.", "pastEarnings": "Past earnings", "paymentWallet": "Payment Wallet", "pending": "Pending", "pending2": "Pending", "phone": "phone", "phoneSupport": "Phone Support", "phoneSupportDesc": "Call us", "phoneVerification": "Phone Verification", "phone_number": "Phone Number", "platformCommission": "Platform commission", "pleaseAcceptTheServiceAgreement": "Please accept the service agreement", "pleaseComplete": "Please complete the recharge \n within the 30 minutes", "pleaseDepositSomeAmountToContinue": "Please deposit some amount to continue", "pleaseEnterAValidAmount": "Please enter a valid amount", "pleaseEnterAValidInvestmentAmount": "Please enter a valid investment amount", "pleaseSelectAnInvestmentProduct": "Please select an investment product", "pleaseWait": "Please wait!", "portfolio": "Portfolio", "preview": "Preview", "privacyPolicy": "Privacy Policy", "proceedToLogin": "Proceed to <PERSON>gin", "processing": "Processing", "productName": "Product Name", "products": "Products", "profile": "Profile", "profit": "Profit", "profitAmount": "Profit <PERSON>", "profitRatio": "Profit ratio", "profitTimes": "Profit Times", "profitWallet": "Profit Wallet", "progress": "Progress", "purchase": "Purchase", "purchaseAmountBetween": "Purchase amount must be between {} and {}", "purchaseContracts": "Purchase Contracts", "purchaseDate": "Purchase date", "purchaseList": "Purchase List", "purchasePrice": "Purchasing price", "purchaseTime": "Purchase time", "purchased": "Purchased", "purchasedContracts": "Purchased Contracts", "purchasedProducts": "Purchased Products", "qrCodeScan": "Please scan the QR code", "readLess": "Less", "readMore": "More", "readyToUpdate": "Ready to Update", "realtime": "Realtime", "reason": "Reason", "rechargeAddress": "Recharge Address", "rechargeOrderSubmittedSuccessfully": "Recharge order submitted successfully", "rechargeQr": "Recharge QR code", "records": "Records", "referAndEarn": "Refer & Earn", "registerSuccess": "Your account has been created, Please login again.", "rejected": "Rejected", "report": "Report a problem", "requestSuccess": "Request is successful", "resendCode": "Resend Code", "reservedPhone": "Reserved Phone", "resetPassword": "Reset Password", "retracementRate": "Retracement Rate", "retry": "Retry", "retryUpdate": "Retry", "returnRate": "Return Rate", "revenue": "Revenue", "revenueDetails": "Revenue details", "reviewFailed": "Review failed, please submit again", "search": "Search US stocks...", "secondGeneration": "Second Generation", "seconds": "seconds", "secondsUpper": "Seconds", "security": "Security", "securityOptionsLabel": "Security Options", "securitySettings": "Security Settings", "seeAll": "See All", "seeMore": "See More", "selectWithdrawalAddress": "Select Withdrawal Address", "sell": "<PERSON>ll", "sellDate": "Sell date", "sellPrice": "<PERSON><PERSON>", "sellQuantity": "Sell quantity", "sellTime": "<PERSON><PERSON>", "sellingPrice": "Selling price", "sellingTime": "Selling time", "sendCode": "Send Code", "sendCodeAlert": "The code has been sent to your account", "sendCodeToEmail": "Send code", "send_code": "Send Code", "serviceAgreement": "Service Agreement", "setAmount": "Set Amount", "setWalletPassword": "Set Wallet Password", "settled": "Settled", "share": "Share", "shareText": "Register your referral with invitation link below", "signIn": "Sign In", "signUp": "Sign Up", "signinAgree": "By signing up, I agree with the ", "signingYouOut": "Signing you out...", "singleAmount": "Single Amount", "size": "Size", "skip": "<PERSON><PERSON>", "skipUpdate": "<PERSON><PERSON>", "smallRechargesBelow": "Small recharges below $100 will not be credited", "smartInvestment": "Smart Investment", "smartInvestmentCycle": "Smart Investment Cycle", "smartInvestmentProducts": "Smart Investment Products", "soldOut": "Sold Out", "somethingWentWrong": "Something went wrong", "somethingWentWrongTryAgain": "Something went wrong, Please try again", "starMentor": "Star Mentor", "startTime": "Start Time", "statistics": "Statistics", "stockCode": "Stock Code", "stockName": "Stock Name", "stockTrading": "Stock Trading", "stocks": "Stocks", "storagePermissionRequired": "Storage Permission Required", "subject": "Subject", "submit": "Submit", "submitRecharge": "Submit Recharge", "submitRequest": "Submit Request", "successful": "Successful", "successfully": "Successfully", "suffixAddText": "Add +", "summary": "Summary", "summaryTransfer": "Are you sure to transfer the following amount?", "summaryWithdraw": "Are you sure to withdraw the following amount?", "support": "Support", "systemNotifications": "System Notifications", "tasks": "Tasks", "tc": "T&C ", "termsAndConditions": "Terms & Conditions", "theStatisticalSample": "The statistical sample is the data based on $10000 in the past 30 days.", "thirdGeneration": "Third Generation", "thirtyMin": "30 minutes", "thisRechargeAddressOneTime": "This recharge address is a one-time address", "timesUpper": "Times", "toCollectionWallet": " to Collection Wallet", "toCommunity": "To Community", "toCommunityWallet": " to Community Wallet", "toDepositWallet": " to Depo<PERSON>t <PERSON>", "toProfitWallet": " to Profit <PERSON><PERSON>", "todayProfit": "Today's profit", "todaysChange": "Today's\nChange", "todaysStockMarket": "Today's Stock Market", "toolTipWarning2": "The contract has reached the maximum operating scale and cannot be purchased temporarily", "tootTipWarning": "There are always risks\ninvolved with investing so\nit's important to do your\nown research and\nunderstand the potential\ndownsides.", "totalAmount": "Total Amount", "totalMembers": "Total Members", "totalRevenue": "Total revenue", "tradingDays": "Trading Days", "tradingWallet": "Trading Wallet", "transactionCycle": "Transaction Cycle", "transactionHash": "Transaction Hash", "transactionHashDescription": "🛡️ Used to track and verify transaction status on the blockchain", "transactionHashDescription2": "Think of it as a digital receipt for your transaction.", "transactionRecords": "Transaction Records", "transactionsHistory": "Transactions History", "transfer": "Transfer", "transferChargedAmount": "The remaining {} will incur a 20% fee ({} in total)", "transferExpectedReceive": "The expected amount to receive is {}", "transferFreeAmount": "You can transfer {} for free", "transferTo": "Transfer ", "transferred": "Transferred", "trc20": "TRC 20", "trc20_description": "• Always 64 characters (hex format)\n• No 0x at the beginning (e.g., 59d24d4463a49e993dfe456317fe1b9e62e4b7817a9f44702c4c2139aabfcd2e)", "trc20_title": "✅ TRC20 (TRON network):", "tryAgain": "Try Again", "tutorCommission": "Tutor Commission", "type": "Type", "typeBack": "back", "typeCollection": "collection", "typeConfirmWallet": "confirmWallet", "typeDeposit": "deposit", "typeFront": "front", "typeGoogle": "google", "typePaymentERC": "ERC20", "typePaymentTRC": "TRC20", "unBindSuccess": "Unbinded successfully", "unbind": "Unbind", "unknown": "Unknown", "unknownVersion": "Unknown", "up": "Up", "upcoming": "Upcoming", "updateNow": "Update Now", "updateRequired": "Update Required", "updating": "Updating...", "uploadBackIdCard": "Upload back of ID/Passport/DL", "uploadFrontIdCard": "Upload front of ID/Passport/DL", "uploadIdBack": "Upload the ID/Passport/DL back", "uploadIdCard": "Upload ID/Passport/DL", "uploadIdFront": "Upload the ID/Passport/DL front", "uploadImageError": "File size is too large", "urlPrefix": "https://", "usMarket": "US Market", "usdt": "USDT", "user": "User", "validAddressNameMsg": "Enter a valid address name", "verify": "Verify", "viewAll": "View All", "vipLevelError": "You need to reach the {} level to purchase", "vipNotice": "VIP Level Insufficient", "vipNoticeDescription": "Minimum VIP Level {} required to follow this mentor", "volume": "Volume", "wallet": "wallet", "walletAddressExists": "The withdraw address already exists", "walletNameExists": "The address name already exists", "walletPass": "Wallet Pass", "walletPassword": "Wallet Password", "walletPasswordShouldBe": "Wallet Password should be 6 characters", "walletUpdatedToast": "Wallet Password Updated", "warning": "Warning", "weHaveSent": "We have sent the one-time password to your registered email", "week": "Week", "welcome": "Welcome!", "whatIsATransactionHash": "What is a Transaction Hash (TxHash)?", "whatIsATransactionHashDescription": "A transaction hash is a unique ID assigned to every transaction on a blockchain. It looks like a long string of letters and numbers (e.g., 0x59d24d44... or 59d24d44...).", "whatsappSupport": "WhatsApp Support", "whatsappSupportDesc": "Contact us via WhatsApp", "willBeSentToProfitWallet": "Will be sent to profit wallet", "winRate": "Win Rate", "withdraw": "Withdraw", "withdrawAddressHintText": "Please enter the withdrawal address", "withdrawAddressLabelText": "<PERSON><PERSON><PERSON> Address", "withdrawAddresses": "Withdraw Addresses", "withdrawHistory": "Withdrawal History", "withdrawInvestment": "Withdraw Investment", "withdrawLimit": "You cannot withdraw below $100", "withdrawal": "<PERSON><PERSON><PERSON>", "withdrawalAmount": "Withdrawal amount", "withdrawalFee": "<PERSON><PERSON><PERSON> Fee :", "withdrawnProfit": "Withdrawn profit", "workingAge": "Working Age", "years": "years", "yearsOfExperience": "Years of Experience", "zeroContractToast": "No contracts available", "unavailableFunds": "Unavailable funds", "availableFunds": "Available funds"}