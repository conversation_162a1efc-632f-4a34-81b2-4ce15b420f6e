{"app": {"name": "SF India : Smart Crypto", "update": {"available": "App Update Available", "required": "Update Required", "forceUpdateRequired": "Update Required", "downloading": "Downloading Update...", "installing": "Installing Update...", "ready": "Ready to Update", "failed": {"download": "Failed to download update", "install": "Failed to install update"}, "permissions": {"install": "Install Permission Required", "storage": "Storage Permission Required"}, "actions": {"updateNow": "Update Now", "skip": "Skip Update", "retry": "Retry"}}, "maintenance": "Maintenance Notice", "version": {"current": "Current Version", "unknown": "Unknown"}}, "auth": {"login": {"title": "Log In", "username": "User Name", "usernameHint": "<PERSON><PERSON> Username", "password": "Password", "passwordHint": "Enter Password", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "alreadyHaveAccount": "Already have an account?", "link": "http://supfut.com/login", "linkLabel": "Login Link"}, "signup": {"title": "Sign Up", "dontHaveAccount": "Don't have account ?", "agreement": "By signing up, I agree with the", "termsAndConditions": "Terms & Conditions", "privacyPolicy": "Privacy Policy", "effectiveDate": "27/06/2023", "checkboxError": "Please indicate that you have read and agree to the Terms and Conditions and Privacy Policy"}, "verification": {"email": "Email Verification Code", "phone": "Phone Verification", "otp": "E-mail verification code", "otpPhone": "SMS verification code", "google": "Google Authentication Code", "googleHint": "Please enter the google code", "googleLabel": "Google Authentication Code", "googleTitle": "Set up Two factor Authentication", "googleSubtitle": "Download Google Authenticator App", "googleSubtitle2": "Android, IOS - Google Authenticator", "resendCode": "Resend Code", "didntReceive": "Didn't receive a code?", "enterCode": "Enter Code", "enterEmailCode": "Enter the email verification code", "enterGoogleCode": "Enter the Google Authentication Code", "enterVerification": "Enter Verification Code", "enterExactly6Digits": "Enter exactly 6 digits", "enterValidGoogleCode": "Enter valid Google Code", "googleCodeShouldBe": "Google code should be 6 characters", "otpCodeError": "Enter exactly 6 digits", "sendCode": "Send Code", "sendCodeAlert": "The code has been sent to your account", "sendCodeToEmail": "Send code", "weHaveSent": "We have sent the one-time password to your registered email"}, "logout": {"title": "Log Out", "confirmation": "Are you sure you want to logout?", "signingOut": "Signing you out..."}, "password": {"change": "Change password", "changeLogin": "Change Login Password", "changePayment": "Change payment password", "changeWallet": "Change wallet password", "new": "New Wallet Password", "confirm": "Confirm Password", "confirmHint": "Confirm Password", "confirmLabel": "Confirm Password", "notEqual": "Enter the matching password", "updated": "Password updated successfully", "updatedToast": "Password Updated", "loginUpdatedToast": "Login Password Updated", "walletUpdatedToast": "Wallet Password Updated", "validation": "Your password must be at least 8 characters long, contain at least one number and have a mixture of uppercase and lowercase letters.", "atLeast8Chars": "At least 8 characters, must contain numbers, at least one upper case and lower case alphabet and must not contain spaces", "atLeast8CharsNoUpper": "At least 8 characters, must contain numbers, one lower case alphabet and must not contain spaces"}, "googleAuth": {"title": "Google Authentication", "change": "Change google authentication code", "modify": "Modify google authentication code", "codeUpdated": "Google Code Updated"}, "phone": {"bind": "Bind Mobile Number", "change": "Change mobile number", "new": "New Phone Number", "numberUpdatedToast": "Phone Number Updated", "reserved": "Reserved Phone"}, "identity": {"bind": "Bind ID/Passport/DL", "change": "Change Identity", "upload": "Upload ID/Passport/DL", "uploadFront": "Upload the ID/Passport/DL front", "uploadBack": "Upload the ID/Passport/DL back", "front": "ID/Passport/DL Front", "back": "ID/Passport/DL Back", "number": "ID/Passport/DL Number", "numberHint": "xxxxxxxxxxxx", "numberError": "Enter a valid ID/Passport/DL Number", "confirmation": "ID/Passport/DL Confirmation", "certificationCompleted": "Verification is already completed", "types": {"aadhaar": "<PERSON><PERSON><PERSON><PERSON>", "idCard": "ID Card", "passport": "Passport", "driverLicense": "Driver License"}}, "account": {"created": "Your account has been created, Please login again.", "approved": "Your account has been approved", "adminVerify": "Your account has been created, Please login again.", "proceedToLogin": "Proceed to <PERSON>gin", "completeInfo": "Complete your account information", "adminWillVerify": "An admin will verify your information after that you can start using the platform"}}, "user": {"profile": {"title": "Profile", "build": "Build your profits", "information": "Account Information", "memberInfo": "Member Information", "name": "Name", "email": "Email", "phone": "Phone Number", "jobPosition": "Job Position", "officeCompany": "Office Company", "workingAge": "Working Age", "yearsOfExperience": "Years of Experience"}, "settings": {"title": "Settings", "language": "Choose Language", "darkMode": "Dark Mode", "security": "Security Settings", "securityOptions": "Security Options"}}, "wallet": {"title": "Wallet", "choose": "<PERSON><PERSON>", "open": "Open Wallet", "password": {"title": "Wallet Password", "set": "Set Wallet Password", "change": "Change wallet password", "confirm": "Confirm Wallet Password", "confirmLabel": "Confirm Wallet Password", "shouldBe6Chars": "Wallet Password should be 6 characters", "enter": "Enter Wallet Password", "updated": "Wallet Password Updated"}, "types": {"bonus": "Bonus Wallet", "collection": "Collection Wallet", "community": "Community Wallet", "deposit": "<PERSON><PERSON><PERSON><PERSON>", "funding": "Funding Wallet", "payment": "Payment Wallet", "profit": "Profit Wallet", "trading": "Trading Wallet"}, "balance": {"available": "Available Balance", "locked": "Locked Balance", "insufficient": "Insufficient balance", "insufficientAvailable": "Insufficient available balance", "noBalance": "No balance available", "noBalanceInfo": "No balance information available"}, "address": {"title": "Address", "name": "Address Name", "nameHint": "Please enter the address name", "nameLabel": "Address Name", "withdrawal": "<PERSON><PERSON><PERSON> Address", "withdrawalHint": "Please enter the withdrawal address", "withdrawalLabel": "<PERSON><PERSON><PERSON> Address", "add": "Add <PERSON>drawal Address +", "addTitle": "<PERSON><PERSON> Address", "delete": "Delete Address", "deleteConfirmation": "Are you sure you want to delete this address?", "select": "Select Withdrawal Address", "exists": "The withdraw address already exists", "nameExists": "The address name already exists", "validName": "Enter a valid address name", "warning": "Only support 6 withdrawal addresses and can not be changed.", "addresses": "Withdraw Addresses"}, "transfer": {"title": "Transfer", "to": "Transfer ", "toCollection": " to Collection Wallet", "toCommunity": " to Community Wallet", "toDeposit": " to Depo<PERSON>t <PERSON>", "toProfit": " to Profit <PERSON><PERSON>", "summary": "Are you sure to transfer the following amount?", "freeAmount": "You can transfer {} for free", "chargedAmount": "The remaining {} will incur a 20% fee ({} in total)", "expectedReceive": "The expected amount to receive is {}", "transferred": "Transferred"}, "withdrawal": {"title": "Withdraw", "amount": "Withdrawal amount", "enterAmount": "Enter withdrawal amount", "summary": "Are you sure to withdraw the following amount?", "fee": "<PERSON><PERSON><PERSON> Fee :", "limit": "You cannot withdraw below $100", "minimum": "Minimum withdrawal amount is", "maximum": "Maximum withdrawal amount is", "history": "Withdrawal History", "investment": "Withdraw Investment"}, "recharge": {"title": "<PERSON><PERSON><PERSON><PERSON>", "address": "Recharge Address", "qr": "Recharge QR code", "submit": "Submit Recharge", "orderSubmitted": "Recharge order submitted successfully", "oneTimeAddress": "This recharge address is a one-time address", "smallRecharges": "Small recharges below $100 will not be credited", "tip": "What is a Transaction Hash (TxHash)?", "whatIsHash": "What is a Transaction Hash (TxHash)?", "hashDescription": "A transaction hash is a unique ID assigned to every transaction on a blockchain. It looks like a long string of letters and numbers (e.g., 0x59d24d44... or 59d24d44...).", "hashDescription2": "🛡️ Used to track and verify transaction status on the blockchain", "hashDescription3": "Think of it as a digital receipt for your transaction.", "enterHash": "Enter Transaction Hash", "enterValidHash": "Please enter a valid transaction hash", "enterValidErc": "Enter a valid ERC20 address", "enterValidTrc": "Enter a valid TRC20 address", "erc20": {"title": "✅ ERC20 (ETH/BSC/Polygon, etc.):", "description": "• Always 66 characters (hex format)\n• Starts with 0x (e.g., 0x59d24d4463a49e993dfe456317fe1b9e62e4b7817a9f44702c4c2139aabfcd2e)"}, "trc20": {"title": "✅ TRC20 (TRON network):", "description": "• Always 64 characters (hex format)\n• No 0x at the beginning (e.g., 59d24d4463a49e993dfe456317fe1b9e62e4b7817a9f44702c4c2139aabfcd2e)"}, "types": {"erc20": "ERC 20", "trc20": "TRC 20"}}}, "contract": {"title": "Contracts", "list": "Contract List", "my": "My Contracts", "available": "Available Contracts", "purchased": "Purchased Contracts", "purchase": "Purchase Contracts", "announcement": "Contract Announcement", "type": "Contract Type", "types": {"conservative": "conservative", "robust": "robust", "radical": "radical"}, "details": {"price": "Contract Price", "size": "Contract Size", "amount": "Contracted Amount", "cycle": "Cycle", "startTime": "Start Time", "endTime": "End Time", "createTime": "Create Time", "orderDate": "Order Date", "orderNo": "Order No", "purchaseDate": "Purchase date", "purchaseTime": "Purchase time", "purchasePrice": "Purchasing price", "purchaseAmount": "Purchase Amount", "purchaseQuantity": "Buy quantity", "sellDate": "Sell date", "sellPrice": "<PERSON><PERSON>", "sellQuantity": "Sell quantity", "sellTime": "<PERSON><PERSON>", "sellingPrice": "Selling price", "sellingTime": "Selling time", "buyPrice": "Buy Price", "buyQuantity": "Buy quantity", "buyingPrice": "Buying price", "buyingTimeNotice": "Please note that after 2:00 PM (ET) each day, buying will no longer be available. Thank you for your understanding and support!"}, "status": {"pending": "Pending", "processing": "Processing", "completed": "Completed", "finished": "Finished", "canceled": "Canceled", "rejected": "Rejected", "settled": "Settled", "soldOut": "Sold Out"}, "actions": {"buy": "Buy", "buyIt": "Buy it", "buyPosition": "Buy position", "sell": "<PERSON>ll", "cancel": "Cancel", "unbind": "Unbind", "oneClickPurchase": "One Click Purchase"}, "investment": {"amount": "Investment Amount", "units": "Investment Units", "additional": "Additional Investment", "confirm": "Confirm Investment", "minimum": "Minimum Purchase Amount", "maximum": "Maximum Purchase Amount", "amountBetween": "Purchase amount must be between {} and {}", "amountMultiple": "Amount must be a multiple of", "amountUnits": "Amount must be in units of", "amountExceedsMax": "Amount exceeds maximum allowed", "invalidAmount": "Invalid amount", "validAmount": "Please enter a valid amount", "validInvestmentAmount": "Please enter a valid investment amount", "selectProduct": "Please select an investment product", "insufficientBalance": "Insufficient balance", "noBalance": "No balance available", "depositToContinue": "Please deposit some amount to continue", "completeRecharge": "Please complete the recharge \n within the 30 minutes"}, "smart": {"title": "Smart Investment", "products": "Smart Investment Products", "cycle": "Smart Investment Cycle", "oneClick": {"title": "One-click Smart Investment Instructions", "description": "One-click smart investment is highly unified and easy to trade. Analysts will enter the market first in the institutional channel. No operation is required during the one-click smart investment period. Just follow the operation, freeing your hands to avoid forgetting to follow the purchase. Daily profits will be automatically restored."}, "followUp": {"title": "Follow-up Purchase Details", "intelligent": "Intelligent Follow-up Investment", "period": "Purchase Time", "amount": "Follow-up Purchase Amount", "records": "Follow Purchase Records", "details": "Follow Purchase Details"}}, "profit": {"title": "Profit", "amount": "Profit <PERSON>", "ratio": "Profit ratio", "expected": "Expected Profit", "actual": "Actual Profit", "today": "Today's profit", "past": "Past earnings", "withdrawn": "Withdrawn profit", "times": "Profit Times", "lossesTimes": "Losses Times", "winRate": "Win Rate", "returnRate": "Return Rate", "monthlyReturn": "Monthly Return", "cumulativeIncome": "Cumulative income", "earningsLastDay": "Earnings last day"}, "statistics": {"drawdown": "Drawdown", "maxDrawdown": "Max Drawdown", "retracementRate": "Retracement Rate", "tradingDays": "Trading Days", "daysOfHolding": "Days of holding", "daysSinceUnbind": "Days since unbind", "volume": "Volume", "numberOfContracts": "Number of Contracts", "numberOfTransactions": "Number of Transactions"}, "management": {"freezeAmount": "Freeze Amount", "lockPeriod": "Lock Period", "automaticRenewal": "Automatic Renewal", "cancelSuccess": "Cancelled successfully", "unBindSuccess": "Unbinded successfully", "cancelAlert": "After confirming the cancellation, you will continue to enjoy the benefits.", "unbindAlert": "After confirming the unbinding, the full amount in the contract will be returned to your profit wallet after 30 working days, and you will continue to receive income until first 15 working days.", "zeroContractToast": "No contracts available", "toolTipWarning": "There are always risks\ninvolved with investing so\nit's important to do your\nown research and\nunderstand the potential\ndownsides.", "toolTipWarning2": "The contract has reached the maximum operating scale and cannot be purchased temporarily"}}, "market": {"title": "Market", "overview": "Market Overview", "updates": "Market Updates", "search": "Search US stocks...", "stocks": "Stocks", "hotStocks": "Hot Stocks", "stockTrading": "Stock Trading", "usMarket": "US Market", "todaysStockMarket": "Today's Stock Market", "todaysChange": "Today's\nChange", "latestPrice": "Latest Price", "latestTransaction": "Latest\nTransaction\nPrice", "stockCode": "Stock Code", "stockName": "Stock Name", "mainstreamCurrency": "Mainstream\nCurrency", "leadingConcept": "Leading Concept", "leadingIndustry": "Leading Industry", "crypto": "Crypto", "gainers": "Gaine<PERSON>", "losers": "Losers", "up": "Up", "down": "Down", "flat": "Flat", "high": "High", "low": "Low", "open": "Open", "close": "Close"}, "finance": {"title": "Finance", "portfolio": "Portfolio", "revenue": "Revenue", "revenueDetails": "Revenue details", "totalRevenue": "Total revenue", "commission": "Commission", "platformCommission": "Platform commission", "mentorCommission": "Mentor Commission", "tutorCommission": "Tutor Commission", "handlingFee": "Handling Fee", "handlingFeeMessage": "A handling fee is required each time", "withdrawalFee": "<PERSON><PERSON><PERSON> Fee :", "fundingThreshold": "Funding Threshold", "additionalAmount": "Additional amount", "additionalFunds": "Additional funds", "interestAmount": "Interest Amount", "interestRate": "Interest Rate", "initialCapital": "Initial capital", "unavailableFunds": "Unavailable funds", "availableFunds": "Available funds"}, "community": {"title": "Community", "members": "Members", "totalMembers": "Total Members", "directMembers": "Direct Members", "otherMembers": "Other Members", "mentors": "Mentors", "celebrityMentor": "Celebrity Mentor", "starMentor": "Star Mentor", "expert": "Expert", "vip": {"levelError": "You need to reach the {} level to purchase", "notice": "VIP Level Insufficient", "description": "Minimum VIP Level {} required to follow this mentor"}, "referral": {"title": "Refer & Earn", "invitationCode": "Invitation code", "invitationCodeError": "Invitation code is invalid", "invitationDescription": "Invest in the future today! Download the SF India : Smart Crypto now and sign up to get started.", "invitationLink": "Invitation Link", "invitationLinkHint": "http://supfut.com?invitationcode=abcdefg", "shareText": "Register your referral with invitation link below", "enterFriendsEmail": "Enter your friends email"}, "follow": {"title": "Follow", "following": "Following", "followPurchaseDetails": "Follow Purchase Details", "followPurchaseRecords": "Follow Purchase Records"}}, "news": {"title": "News", "updates": "News Updates"}, "support": {"title": "Support", "helpCenter": "Help Center", "customerService": "Customer Service", "customerSupport": "Customer Support", "contact": "Contact Support", "liveChat": "Live Chat", "liveChatDesc": "Chat with our support team", "email": "Email Support", "emailDesc": "Send us an email", "phone": "Phone Support", "phoneDesc": "Call us", "whatsapp": "WhatsApp Support", "whatsappDesc": "Contact us via WhatsApp", "report": "Report a problem"}, "common": {"actions": {"confirm": "Confirm", "cancel": "Cancel", "submit": "Submit", "save": "Save", "edit": "Edit", "delete": "Delete", "deleteConfirmation": "Confirm Delete", "change": "Change", "add": "Add", "remove": "Remove", "close": "Close", "done": "Done", "next": "Next", "back": "Back", "skip": "<PERSON><PERSON>", "retry": "Retry", "tryAgain": "Try Again", "continue": "Continue", "proceed": "Proceed", "viewAll": "View All", "seeAll": "See All", "seeMore": "See More", "readMore": "More", "readLess": "Less", "copy": "Copy", "copied": "<PERSON>pied", "copiedClipboard": "Copied to clipboard", "copyCode": "Copy the following code", "share": "Share", "download": "Download", "upload": "Upload", "preview": "Preview", "search": "Search", "filter": "Filter", "sort": "Sort", "refresh": "Refresh", "sync": "Sync"}, "status": {"success": "Success", "successful": "Successful", "successfully": "Successfully", "error": "Error", "failed": "Failed", "pending": "Pending", "processing": "Processing", "completed": "Completed", "finished": "Finished", "canceled": "Canceled", "rejected": "Rejected", "approved": "Approved", "verified": "Verified", "unverified": "Unverified", "active": "Active", "inactive": "Inactive", "enabled": "Enabled", "disabled": "Disabled", "connected": "Connected", "disconnected": "Disconnected", "online": "Online", "offline": "Offline", "available": "Available", "unavailable": "Unavailable", "visible": "Visible", "hidden": "Hidden", "public": "Public", "private": "Private", "locked": "Locked", "unlocked": "Unlocked", "expired": "Expired", "valid": "<PERSON><PERSON>", "invalid": "Invalid"}, "time": {"justNow": "just now", "seconds": "seconds", "secondsUpper": "Seconds", "oneMin": "1 minute", "fiveMin": "5 minutes", "fifteenMin": "15 minutes", "thirtyMin": "30 minutes", "anHourAgo": "an hour ago", "hoursAgo": "hours ago", "aDayAgo": "a day ago", "daysAgo": "days ago", "aMonthAgo": "a month ago", "day": "day", "days": "days", "week": "Week", "month": "Month", "year": "Year", "years": "years", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "yearly": "Yearly"}, "navigation": {"home": "Home", "market": "Market", "finance": "Finance", "community": "Community", "news": "News", "profile": "Profile", "settings": "Settings", "support": "Support", "notifications": "Notifications", "chat": "Cha<PERSON>", "chats": "Chats", "tasks": "Tasks", "missions": "Missions"}, "form": {"required": "Required", "optional": "Optional", "enter": "Enter", "select": "Select", "choose": "<PERSON><PERSON>", "upload": "Upload", "browse": "Browse", "dragAndDrop": "Drag and drop files here", "fileSize": "File size", "maxFileSize": "Maximum file size", "supportedFormats": "Supported formats", "validation": {"required": "This field is required", "invalid": "Invalid value", "tooShort": "Too short", "tooLong": "Too long", "invalidFormat": "Invalid format", "passwordMismatch": "Passwords do not match", "invalidEmail": "Invalid email address", "invalidPhone": "Invalid phone number", "invalidUrl": "Invalid URL", "invalidNumber": "Invalid number", "minValue": "Minimum value is {}", "maxValue": "Maximum value is {}", "minLength": "Minimum length is {}", "maxLength": "Maximum length is {}"}}, "messages": {"success": "Operation completed successfully", "error": "An error occurred", "warning": "Warning", "info": "Information", "confirm": "Are you sure?", "loading": "Loading...", "noData": "No data available", "noResults": "No results found", "empty": "No data yet", "somethingWentWrong": "Something went wrong", "tryAgainLater": "Please try again later", "networkError": "Network Error", "noInternet": "Please validate your network connection", "permissionDenied": "Permission denied", "fileTooLarge": "File size is too large", "invalidFileType": "Invalid file type", "uploadFailed": "Upload failed", "downloadFailed": "Download Failed", "downloadComplete": "Download Complete", "downloadUrlNotAvailable": "Download URL not available", "installationComplete": "Installation Complete", "installationFailed": "Installation Failed"}, "placeholders": {"search": "Search...", "email": "Enter email", "phone": "Enter phone number", "password": "Enter password", "username": "Enter username", "name": "Enter name", "address": "Enter address", "amount": "Enter amount", "code": "Enter code", "message": "Enter message", "comment": "Enter comment", "description": "Enter description"}, "labels": {"name": "Name", "email": "Email", "phone": "Phone Number", "password": "Password", "username": "Username", "address": "Address", "amount": "Amount", "code": "Code", "message": "Message", "comment": "Comment", "description": "Description", "type": "Type", "status": "Status", "date": "Date", "time": "Time", "quantity": "Quantity", "price": "Price", "total": "Total", "balance": "Balance", "currency": "<PERSON><PERSON><PERSON><PERSON>", "language": "Language", "country": "Country", "city": "City", "state": "State", "zipCode": "Zip Code"}}}