import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primaryColor: ColorPalette.primaryColor,
      dividerColor: Colors.transparent,
      colorScheme: const ColorScheme.light(),
      extensions: <ThemeExtension<MyColorScheme>>[
        MyColorScheme.lightScheme,
      ],
      appBarTheme: AppBarTheme(
        backgroundColor: ColorPalette.white,
        iconTheme: const CupertinoIconThemeData(),
      ),
      scaffoldBackgroundColor: ColorPalette.backgroundColor,
      textTheme: textLightTheme,
      primaryTextTheme: textLightTheme,
      fontFamily: FontPalette.themeFont,
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(foregroundColor: ColorPalette.primaryColor),
      ),
      textSelectionTheme:
          const TextSelectionThemeData(cursorColor: Colors.black),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: ColorPalette.backgroundColor,
      ),
      bottomSheetTheme: BottomSheetThemeData(
        backgroundColor: ColorPalette.backgroundColor,
      ),
      bottomAppBarTheme: BottomAppBarTheme(
        color: ColorPalette.backgroundColor,
      ),
      iconButtonTheme: IconButtonThemeData(
        style: IconButton.styleFrom(foregroundColor: ColorPalette.black),
      ),
      dialogBackgroundColor: ColorPalette.backgroundColor,
      dialogTheme: DialogTheme(
        backgroundColor: ColorPalette.backgroundColor,
        iconColor: ColorPalette.black,
      ),
      iconTheme: IconThemeData(color: ColorPalette.black),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: ColorPalette.primaryColorDark,
      dividerColor: ColorPalette.lightGrey4,
      colorScheme: const ColorScheme.dark(),
      extensions: <ThemeExtension<MyColorScheme>>[
        MyColorScheme.darkScheme,
      ],
      scaffoldBackgroundColor: ColorPalette.backgroundColorDark,
      textTheme: textDarkTheme,
      primaryTextTheme: textDarkTheme,
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: ColorPalette.primaryColorDark,
        ),
      ),
      textSelectionTheme:
          TextSelectionThemeData(cursorColor: ColorPalette.white),
      fontFamily: FontPalette.themeFont,
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        backgroundColor: ColorPalette.cardColorDark,
      ),
      bottomSheetTheme: BottomSheetThemeData(
        backgroundColor: ColorPalette.cardColorDark,
      ),
      bottomAppBarTheme: BottomAppBarTheme(
        color: ColorPalette.cardColorDark,
      ),
      iconButtonTheme: IconButtonThemeData(
        style: IconButton.styleFrom(
          foregroundColor: ColorPalette.white,
        ),
      ),
      dialogBackgroundColor: ColorPalette.backgroundColorDark,
      dialogTheme: DialogTheme(
        backgroundColor: ColorPalette.backgroundColorDark,
        iconColor: ColorPalette.white,
      ),
      iconTheme: IconThemeData(color: ColorPalette.white),
    );
  }

  static TextTheme get textDarkTheme {
    return Typography.englishLike2018.apply(
      fontSizeFactor: 0.8.sp,
      bodyColor: ColorPalette.titleColorDark,
      fontFamily: FontPalette.themeFont,
    );
  }

  static TextTheme get textLightTheme {
    return Typography.englishLike2018.apply(
      fontSizeFactor: 0.8.sp,
      bodyColor: ColorPalette.titleColor,
      fontFamily: FontPalette.themeFont,
    );
  }
}
