import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class FontPalette {
  static const themeFont = "Poppins";

  //light

  static TextStyle light8 =
      TextStyle(fontSize: 8.sp, fontWeight: FontWeight.w300, letterSpacing: 0);

  static TextStyle light9 =
      TextStyle(fontSize: 9.sp, fontWeight: FontWeight.w300, letterSpacing: 0);

  static TextStyle light10 =
      TextStyle(fontSize: 10.sp, fontWeight: FontWeight.w300, letterSpacing: 0);

  static TextStyle light11 =
      TextStyle(fontSize: 11.sp, fontWeight: FontWeight.w300, letterSpacing: 0);

  static TextStyle light12 =
      TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w300, letterSpacing: 0);

  static TextStyle light13 =
      TextStyle(fontSize: 13.sp, fontWeight: FontWeight.w300, letterSpacing: 0);

  static TextStyle light14 =
      TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w300, letterSpacing: 0);

  static TextStyle light15 =
      TextStyle(fontSize: 15.sp, fontWeight: FontWeight.w300, letterSpacing: 0);

  static TextStyle light16 =
      TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w300, letterSpacing: 0);

  static TextStyle light17 =
      TextStyle(fontSize: 17.sp, fontWeight: FontWeight.w300, letterSpacing: 0);

  static TextStyle light18 =
      TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w300, letterSpacing: 0);

  static TextStyle light19 =
      TextStyle(fontSize: 19.sp, fontWeight: FontWeight.w300, letterSpacing: 0);

  static TextStyle light20 =
      TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w300, letterSpacing: 0);

  static TextStyle light21 =
      TextStyle(fontSize: 21.sp, fontWeight: FontWeight.w300, letterSpacing: 0);

  static TextStyle light22 =
      TextStyle(fontSize: 22.sp, fontWeight: FontWeight.w300, letterSpacing: 0);

  static TextStyle light23 =
      TextStyle(fontSize: 23.sp, fontWeight: FontWeight.w300, letterSpacing: 0);

  static TextStyle light24 =
      TextStyle(fontSize: 24.sp, fontWeight: FontWeight.w300, letterSpacing: 0);

  static TextStyle light25 =
      TextStyle(fontSize: 25.sp, fontWeight: FontWeight.w300, letterSpacing: 0);

  //normal

  static TextStyle normal8 =
      TextStyle(fontSize: 8.sp, fontWeight: FontWeight.w400, letterSpacing: 0);

  static TextStyle normal9 =
      TextStyle(fontSize: 9.sp, fontWeight: FontWeight.w400, letterSpacing: 0);

  static TextStyle normal10 =
      TextStyle(fontSize: 10.sp, fontWeight: FontWeight.w400, letterSpacing: 0);

  static TextStyle normal11 =
      TextStyle(fontSize: 11.sp, fontWeight: FontWeight.w400, letterSpacing: 0);

  static TextStyle normal12 =
      TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w400, letterSpacing: 0);

  static TextStyle normal13 =
      TextStyle(fontSize: 13.sp, fontWeight: FontWeight.w400, letterSpacing: 0);

  static TextStyle normal14 =
      TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w400, letterSpacing: 0);

  static TextStyle normal15 =
      TextStyle(fontSize: 15.sp, fontWeight: FontWeight.w400, letterSpacing: 0);

  static TextStyle normal16 =
      TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w400, letterSpacing: 0);

  static TextStyle normal17 =
      TextStyle(fontSize: 17.sp, fontWeight: FontWeight.w400, letterSpacing: 0);

  static TextStyle normal18 =
      TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w400, letterSpacing: 0);

  static TextStyle normal19 =
      TextStyle(fontSize: 19.sp, fontWeight: FontWeight.w400, letterSpacing: 0);

  static TextStyle normal20 =
      TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w400, letterSpacing: 0);

  static TextStyle normal21 =
      TextStyle(fontSize: 21.sp, fontWeight: FontWeight.w400, letterSpacing: 0);

  static TextStyle normal22 =
      TextStyle(fontSize: 22.sp, fontWeight: FontWeight.w400, letterSpacing: 0);

  static TextStyle normal23 =
      TextStyle(fontSize: 23.sp, fontWeight: FontWeight.w400, letterSpacing: 0);

  static TextStyle normal24 =
      TextStyle(fontSize: 24.sp, fontWeight: FontWeight.w400, letterSpacing: 0);

  static TextStyle normal25 =
      TextStyle(fontSize: 25.sp, fontWeight: FontWeight.w400, letterSpacing: 0);

  //

  static TextStyle medium9 =
      TextStyle(fontSize: 9.sp, fontWeight: FontWeight.w500, letterSpacing: 0);

  static TextStyle medium10 =
      TextStyle(fontSize: 10.sp, fontWeight: FontWeight.w500, letterSpacing: 0);

  static TextStyle medium11 =
      TextStyle(fontSize: 11.sp, fontWeight: FontWeight.w500, letterSpacing: 0);

  static TextStyle medium12 =
      TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w500, letterSpacing: 0);

  static TextStyle medium13 =
      TextStyle(fontSize: 13.sp, fontWeight: FontWeight.w500, letterSpacing: 0);

  static TextStyle medium14 =
      TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w500, letterSpacing: 0);

  static TextStyle medium15 =
      TextStyle(fontSize: 15.sp, fontWeight: FontWeight.w500, letterSpacing: 0);

  static TextStyle medium16 =
      TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500, letterSpacing: 0);

  static TextStyle medium17 =
      TextStyle(fontSize: 17.sp, fontWeight: FontWeight.w500, letterSpacing: 0);

  static TextStyle medium18 =
      TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w500, letterSpacing: 0);

  static TextStyle medium19 =
      TextStyle(fontSize: 19.sp, fontWeight: FontWeight.w500, letterSpacing: 0);

  static TextStyle medium20 =
      TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w500, letterSpacing: 0);

  static TextStyle medium21 =
      TextStyle(fontSize: 21.sp, fontWeight: FontWeight.w500, letterSpacing: 0);

  static TextStyle medium22 =
      TextStyle(fontSize: 22.sp, fontWeight: FontWeight.w500, letterSpacing: 0);

  static TextStyle medium23 =
      TextStyle(fontSize: 23.sp, fontWeight: FontWeight.w500, letterSpacing: 0);

  static TextStyle medium24 =
      TextStyle(fontSize: 24.sp, fontWeight: FontWeight.w500, letterSpacing: 0);

  static TextStyle medium25 =
      TextStyle(fontSize: 25.sp, fontWeight: FontWeight.w500, letterSpacing: 0);

  //

  static TextStyle semiBold9 =
      TextStyle(fontSize: 9.sp, fontWeight: FontWeight.w600, letterSpacing: 0);

  static TextStyle semiBold10 =
      TextStyle(fontSize: 10.sp, fontWeight: FontWeight.w600, letterSpacing: 0);

  static TextStyle semiBold11 =
      TextStyle(fontSize: 11.sp, fontWeight: FontWeight.w600, letterSpacing: 0);

  static TextStyle semiBold12 =
      TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w600, letterSpacing: 0);

  static TextStyle semiBold13 =
      TextStyle(fontSize: 13.sp, fontWeight: FontWeight.w600, letterSpacing: 0);

  static TextStyle semiBold14 =
      TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w600, letterSpacing: 0);

  static TextStyle semiBold15 =
      TextStyle(fontSize: 15.sp, fontWeight: FontWeight.w600, letterSpacing: 0);

  static TextStyle semiBold16 =
      TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600, letterSpacing: 0);

  static TextStyle semiBold17 =
      TextStyle(fontSize: 17.sp, fontWeight: FontWeight.w600, letterSpacing: 0);

  static TextStyle semiBold18 =
      TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w600, letterSpacing: 0);

  static TextStyle semiBold19 =
      TextStyle(fontSize: 19.sp, fontWeight: FontWeight.w600, letterSpacing: 0);

  static TextStyle semiBold20 =
      TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w600, letterSpacing: 0);

  static TextStyle semiBold21 =
      TextStyle(fontSize: 21.sp, fontWeight: FontWeight.w600, letterSpacing: 0);

  static TextStyle semiBold22 =
      TextStyle(fontSize: 22.sp, fontWeight: FontWeight.w600, letterSpacing: 0);

  static TextStyle semiBold23 =
      TextStyle(fontSize: 23.sp, fontWeight: FontWeight.w600, letterSpacing: 0);

  static TextStyle semiBold24 =
      TextStyle(fontSize: 24.sp, fontWeight: FontWeight.w600, letterSpacing: 0);

  static TextStyle semiBold25 =
      TextStyle(fontSize: 25.sp, fontWeight: FontWeight.w600, letterSpacing: 0);

  static TextStyle semiBold30 =
      TextStyle(fontSize: 30.sp, fontWeight: FontWeight.w600, letterSpacing: 0);

  static TextStyle semiBold40 =
      TextStyle(fontSize: 40.sp, fontWeight: FontWeight.w600, letterSpacing: 0);

  static TextStyle semiBold43 =
      TextStyle(fontSize: 40.sp, fontWeight: FontWeight.w600, letterSpacing: 0);

  //

  static TextStyle bold9 =
      TextStyle(fontSize: 9.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold10 =
      TextStyle(fontSize: 10.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold11 =
      TextStyle(fontSize: 11.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold12 =
      TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold13 =
      TextStyle(fontSize: 13.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold14 =
      TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold15 =
      TextStyle(fontSize: 15.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold30 =
      TextStyle(fontSize: 30.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold16 =
      TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold17 =
      TextStyle(fontSize: 17.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold18 =
      TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold19 =
      TextStyle(fontSize: 19.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold20 =
      TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold21 =
      TextStyle(fontSize: 21.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold22 =
      TextStyle(fontSize: 22.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold23 =
      TextStyle(fontSize: 23.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold24 =
      TextStyle(fontSize: 24.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold25 =
      TextStyle(fontSize: 25.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold26 =
      TextStyle(fontSize: 26.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold27 =
      TextStyle(fontSize: 27.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold28 =
      TextStyle(fontSize: 28.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold40 =
      TextStyle(fontSize: 40.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  static TextStyle bold48 =
      TextStyle(fontSize: 48.sp, fontWeight: FontWeight.w700, letterSpacing: 0);

  //

  static TextStyle extraBold9 =
      TextStyle(fontSize: 9.sp, fontWeight: FontWeight.w800, letterSpacing: 0);

  static TextStyle extraBold10 =
      TextStyle(fontSize: 10.sp, fontWeight: FontWeight.w800, letterSpacing: 0);

  static TextStyle extraBold11 =
      TextStyle(fontSize: 11.sp, fontWeight: FontWeight.w800, letterSpacing: 0);

  static TextStyle extraBold12 =
      TextStyle(fontSize: 12.sp, fontWeight: FontWeight.w800, letterSpacing: 0);

  static TextStyle extraBold13 =
      TextStyle(fontSize: 13.sp, fontWeight: FontWeight.w800, letterSpacing: 0);

  static TextStyle extraBold14 =
      TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w800, letterSpacing: 0);

  static TextStyle extraBold15 =
      TextStyle(fontSize: 15.sp, fontWeight: FontWeight.w800, letterSpacing: 0);

  static TextStyle extraBold16 =
      TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w800, letterSpacing: 0);

  static TextStyle extraBold17 =
      TextStyle(fontSize: 17.sp, fontWeight: FontWeight.w800, letterSpacing: 0);

  static TextStyle extraBold18 =
      TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w800, letterSpacing: 0);

  static TextStyle extraBold19 =
      TextStyle(fontSize: 19.sp, fontWeight: FontWeight.w800, letterSpacing: 0);

  static TextStyle extraBold20 =
      TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w800, letterSpacing: 0);

  static TextStyle extraBold21 =
      TextStyle(fontSize: 21.sp, fontWeight: FontWeight.w800, letterSpacing: 0);

  static TextStyle extraBold22 =
      TextStyle(fontSize: 22.sp, fontWeight: FontWeight.w800, letterSpacing: 0);

  static TextStyle extraBold23 =
      TextStyle(fontSize: 23.sp, fontWeight: FontWeight.w800, letterSpacing: 0);

  static TextStyle extraBold24 =
      TextStyle(fontSize: 24.sp, fontWeight: FontWeight.w800, letterSpacing: 0);

  static TextStyle extraBold25 =
      TextStyle(fontSize: 25.sp, fontWeight: FontWeight.w800, letterSpacing: 0);
//
}
