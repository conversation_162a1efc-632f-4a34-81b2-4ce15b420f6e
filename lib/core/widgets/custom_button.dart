import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app_v2/core/theme/color_pallette.dart';
import 'package:sf_app_v2/core/theme/font_pallette.dart';
import 'package:sf_app_v2/core/theme/my_color_scheme.dart';

class CustomButton extends StatelessWidget {
  final void Function()? onPressed;
  final double width;
  final double height;
  final String label;
  final bool? isLoading;
  final double? borderRadiusUser;
  final TextStyle? fontStyle;
  final ButtonStyle? buttonStyle; 
  final TextStyle? textStyle;
  final TextStyle? btnTextStyle;
  final bool? isOutlined;
  final bool isEnabled;
  final bool isShadowEnabled;
  final Color? backgroundColor;
  final Widget? child;
  final EdgeInsets? padding;
  final Widget? leading;
  final double? elevation;
  final Color? loadingColor;
  final bool shimmer;

  const CustomButton({
    super.key,
    this.onPressed,
    required this.width,
    required this.height,
    required this.label,
    this.isLoading,
    this.borderRadiusUser,
    this.fontStyle,
    this.buttonStyle,
    this.btnTextStyle,
    this.isOutlined,
    this.isEnabled = true,
    this.isShadowEnabled = false,
    this.backgroundColor,
    this.textStyle,
    this.child,
    this.padding,
    this.leading,
    this.elevation,
    this.loadingColor,
    this.shimmer = false,
  });

  @override
  Widget build(BuildContext context) {
    final bgColor = backgroundColor ?? myColorScheme(context).primaryColor;

    return shimmer
        ? ShimmerButton(
            width: width,
            height: height,
            borderRadius: borderRadiusUser ?? 56.r,
            backgroundColor: bgColor ?? ColorPalette.primaryColor,
          )
        : SizedBox(
            height: height,
            width: width,
            child: ElevatedButton(
              onPressed: isEnabled && !(isLoading ?? false) ? onPressed : null,
              style: buttonStyle ??
                  ElevatedButton.styleFrom(
                    foregroundColor: (isOutlined ?? false)
                        ? myColorScheme(context).primaryColor
                        : ColorPalette.white,
                    side: (isOutlined ?? false)
                        ? BorderSide(
                            width: 1.r,
                            color: myColorScheme(context).primaryColor ??
                                ColorPalette.primaryColor,
                          )
                        : null,
                    backgroundColor: (isOutlined ?? false)
                        ? myColorScheme(context).cardColor
                        : isEnabled
                            ? bgColor
                            : myColorScheme(context).viewAllColor,
                    elevation: elevation ?? 8,
                    shape: RoundedRectangleBorder(
                      borderRadius:
                          BorderRadius.circular(borderRadiusUser?.r ?? 56.r),
                    ),
                    shadowColor: isShadowEnabled
                        ? ColorPalette.dropShadow
                        : Colors.transparent,
                    disabledBackgroundColor:
                        myColorScheme(context).primaryColor?.withValues(alpha: 0.2),
                  ),
              child: (isLoading ?? false)
                  ? Center(
                      child: SizedBox(
                        height: (height / 2).h,
                        width: (height / 2).w,
                        child: CircularProgressIndicator(
                          strokeWidth: 1.75,
                          color: loadingColor ??
                              myColorScheme(context).primaryColor,
                        ),
                      ),
                    )
                  : child ??
                      Padding(
                        padding: padding ?? EdgeInsets.zero,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            if (leading != null) leading!,
                            if (leading != null) SizedBox(width: 8.w),
                            Flexible(
                              child: FittedBox(
                                child: Text(
                                  label,
                                  overflow: TextOverflow.ellipsis,
                                  style: btnTextStyle ??
                                      FontPalette.bold14.copyWith(
                                        color: isOutlined == true
                                            ? myColorScheme(context)
                                                .primaryColor
                                            : Colors.white,
                                      ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
            ),
          );
  }
}

class ShimmerButton extends StatelessWidget {
  final double width;
  final double height;
  final double borderRadius;
  final Color backgroundColor;

  const ShimmerButton({
    super.key,
    required this.width,
    required this.height,
    required this.borderRadius,
    required this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(borderRadius),
      ),
    );
  }
}
