class StringConstants {
  static const String appName = "app.name";
  static const String invitationDescription = "community.referral.invitationDescription";
  static const String stocks = "market.stocks";
  static const String crypto = "market.crypto";
  static String nameValidatorMsg(int value) =>
      "Must be more than $value characters";
  static String customGenValidatorMsg(String msg) => msg;
  static const String networkErrorMsg =
      "Oops! Something went wrong. Please check your network connection and try again.";

  static const String buildYourProfile = "user.profile.build";
  static const String onBoardScreenSubtitle = "onBoardScreenSubtitle";
  static const String continueTxt = "common.actions.continue";
  static const String letsStart = "letsStart";
  static const String login = "auth.login.title";
  static const String logout = "auth.logout.title";
  static const String forgotPassword = "auth.login.forgotPassword";
  static const String signIn = "auth.login.title";
  static const String welcome = "welcome";
  static const String skip = "common.actions.skip";
  static const String user = "user";
  static const String logoutConfirmation = "auth.logout.confirmation";
  static const String cancel = "common.actions.cancel";
  static const String added = "added";
  static const String idCard = "auth.identity.types.idCard";
  static const String emptyStringMsg = "common.form.validation.required";
  static const String enterPhone = "common.placeholders.phone";
  static const String enterCaptcha = "enterCaptcha";
  static const String captchaCode = "captchaCode";
  static const String emptyPasswordMsg = "common.form.validation.required";
  static const String emptyWalletMsg = "common.form.validation.required";
  static const String emptyWalletPasswordMsg = "common.form.validation.required";
  static const String emptyUserNameMsg = "common.form.validation.required";
  static const String emptyEmailMsg = "common.form.validation.required";
  static const String emptyAddressNameMsg = "common.form.validation.required";
  static const String emptyAddressEmailMsg = "common.form.validation.required";
  static const String loginLabelText = "auth.login.username";
  static const String emailLabelText = "common.labels.email";
  static const String password = "auth.login.password";
  static const String enterYourFriendsEmail = "community.referral.enterFriendsEmail";
  static const String loginHintText = "auth.login.usernameHint";
  static const String passwordHintText = "auth.login.passwordHint";
  static const String confirmHintText = "auth.password.confirmHint";
  static const String invitationCode = "community.referral.invitationCode";
  static const String emailHintText = "common.placeholders.email";
  static const String dontHaveAccount = "auth.signup.dontHaveAccount";
  static const String alreadyHaveAccount = "auth.login.alreadyHaveAccount";
  static const String iAgree = "iHaveReadAndAgreed";
  static const String signUp = "auth.signup.title";
  static const String logIn = "auth.login.title";
  static const String verify = "verify";
  static const String tc = "auth.signup.termsAndConditions";
  static const String and = "and";
  static const String privacyPolicy = "auth.signup.privacyPolicy";
  static const String accountName = "user.profile.name";
  static const String accountInformation = "user.profile.information";
  static const String submit = "common.actions.submit";
  static const String idCardNumber = "auth.identity.number";
  static const String idCardNumberHint = "auth.identity.numberHint";
  static const String idNumberErrorMessage = "auth.identity.numberError";
  static const String uploadIdFront = "auth.identity.uploadFront";
  static const String uploadIdBack = "auth.identity.uploadBack";
  static const String bindIdCard = "auth.identity.bind";
  static const String uploadIdCard = "auth.identity.upload";
  static const String uploadFrontIDCard = "auth.identity.uploadFront";
  static const String uploadBackIDCard = "auth.identity.uploadBack";
  static const String idCardConfirmation = "auth.identity.confirmation";
  static const String idCardFront = "auth.identity.front";
  static const String idCardBack = "auth.identity.back";
  static const String openWallet = "wallet.open";
  static const String walletPassword = "wallet.password.title";
  static const String reservedPhone = "auth.phone.reserved";
  static const String enterPhoneNumber = "common.placeholders.phone";
  static const String googleAuthentication = "auth.googleAuth.title";
  static const String enterGoogleCode = "auth.verification.enterGoogleCode";
  static const String suffixAddText = "suffixAddText";
  static const String setWalletPassword = "wallet.password.set";
  static const String enterWalletPassword = "wallet.password.enter";
  static const String confirmWalletPassword = "wallet.password.confirm";
  static const String phoneVerification = "auth.verification.phone";
  static const String countryDialCode = "countryDialCode";
  static const String googleAuthTitleText = "auth.verification.googleTitle";
  static const String googleAuthSubTitleText = "auth.verification.googleSubtitle";
  static const String googleAuthSubTitle2Text = "auth.verification.googleSubtitle2";
  static const String qrCodeScan = "qrCodeScan";
  static const String copyCode = "common.actions.copyCode";
  static const String code = "common.labels.code";
  static const String googleAuthCode = "auth.verification.google";
  static const String home = "common.navigation.home";
  static const String myContracts = "contract.my";
  static const String community = "common.navigation.community";
  static const String profile = "common.navigation.profile";
  static const String deposit = "wallet.recharge.title";
  static const String withdraw = "wallet.withdrawal.title";
  static const String transfer = "wallet.transfer.title";
  static const String records = "records";
  static const String contracts = "contract.title";
  static const String contractList = "contract.list";
  static const String marketUpdates = "market.updates";
  static const String news = "common.navigation.news";
  static const String newsUpdates = "news.updates";
  static const String viewAll = "common.actions.viewAll";
  static const String seeAll = "common.actions.seeAll";
  static const String accountTotal = "accountTotal";
  static const String contractedAmount = "contract.details.amount";
  static const String availableBalance = "wallet.balance.available";
  static const String freezeAmount = "contract.management.freezeAmount";
  static const String referAndEarn = "community.referral.title";

  static const String bankAccount = "bankAccount";
  static const String bankText = "bankText";
  static const String bank = "bank";
  static const String editBank = "editBank";
  static const String accountNumber = "accountNumber";

  static const String tasks = "common.navigation.tasks";

  static const String information = "user.profile.information";

  static const String missions = "common.navigation.missions";
  static const String security = "user.settings.security";
  static const String products = "products";
  static const String language = "user.settings.language";

  static const String share = "common.actions.share";

  static const String shareText = "community.referral.shareText";

  static const String customerSupport = "support.customerSupport";
  static const String customerService = "support.customerService";
  static const String support = "support.title";
  static const String whatsappSupport = "support.whatsapp";
  static const String whatsappSupportDesc = "support.whatsappDesc";
  static const String emailSupport = "support.email";
  static const String emailSupportDesc = "support.emailDesc";
  static const String liveChat = "support.liveChat";
  static const String liveChatDesc = "support.liveChatDesc";
  static const String phoneSupport = "support.phone";
  static const String phoneSupportDesc = "support.phoneDesc";
  static const String subject = "subject";
  static const String message = "common.labels.message";
  static const String submitRequest = "submitRequest";
  static const String contactSupport = "support.contact";

  static const String helpCenter = "support.helpCenter";

  static const String report = "support.report";

  static const String aboutUs = "aboutUs";

  static const String download = "common.actions.download";

  static const String securityOptionsLabel = "user.settings.securityOptions";

  static const String bindMobile = "auth.phone.bind";

  static const String customValidatorMsg = "customValidatorMsg";

  static const String errorMsg = "common.messages.error";
  static const String errorAuth = "errorAuth";
  static const String emailValidatorMsg = "common.form.validation.invalidEmail";
  static const String passwordValidationMsg = "auth.password.validation";
  static const String passwordNotEqual = "auth.password.notEqual";
  static const String error = "common.status.error";
  static const String noInternet = "common.messages.noInternet";
  static const String validAddressNameMsg = "wallet.address.validName";
  static const String proceedToLogin = "auth.account.proceedToLogin";
  static const String incorrectOtp = "incorrectOtp";
  static const String incorrectCaptcha = "incorrectCaptcha";
  static const String adAdminWill = "auth.account.adminWillVerify";
  static const String idCardUnder = "idCardUnder";
  static const String alertMsgAdminVerify = "auth.account.adminVerify";
  static const String alertProceedToLogin = "auth.account.proceedToLogin";
  static const String purchasedContracts = "contract.purchased";
  static const String purchasedProducts = "purchasedProducts";
  static const String loginLinkLabelText = "auth.login.linkLabel";
  static const String invitationLinkLabelText = "community.referral.invitationLink";
  static const String securitySettings = "user.settings.security";
  static const String changePassword = "auth.password.change";
  static const String changeWalletPassword = "wallet.password.change";
  static const String changeLoginPassword = "auth.password.changeLogin";
  static const String changeGoogleAuthentication = "auth.googleAuth.change";
  static const String changePaymentPassword = "auth.password.changePayment";
  static const String changeNumber = "auth.phone.change";
  static const String changeAuthCode = "auth.googleAuth.modify";
  static const String networkLineLabelText = "networkLineLabelText";
  static const String addressNameTextController = "wallet.address.name";
  static const String addressTextLabelText = "wallet.address.nameLabel";
  static const String addressTextHintText = "wallet.address.nameHint";
  static const String withdrawAddressLabelText = "wallet.address.withdrawalLabel";
  static const String withdrawAddressHintText = "wallet.address.withdrawalHint";
  static const String googleAuthCodeLabelText = "auth.verification.googleLabel";
  static const String googleAuthCodeHintText = "auth.verification.googleHint";
  static const String addAddressWarning = "wallet.address.warning";
  static const String authentication = "authentication";
  static const String sendCodeToEmail = "auth.verification.sendCodeToEmail";
  static const String sendCodeAlert = "auth.verification.sendCodeAlert";
  static const String checkEmail = "checkEmail";
  static const String checkPhone = "checkPhone";
  static const String resetPassword = "auth.login.resetPassword";
  static const String passwordUpdatedToast = "auth.password.updated";
  static const String walletUpdatedToast = "wallet.password.updated";
  static const String numberUpdatedToast = "auth.phone.numberUpdatedToast";
  static const String googleCodeUpdatedToast = "auth.googleAuth.codeUpdated";
  static const String enterCode = "auth.verification.enterCode";
  static const String newWalletPasswordLabel = "auth.password.new";
  static const String confirmWalletPasswordLabel = "wallet.password.confirmLabel";
  static const String confirmPasswordLabel = "auth.password.confirmLabel";
  static const String newPhoneNumberLabel = "auth.phone.new";
  static const String newPassword = "auth.password.new";
  static const String numberOfTransactions = "contract.statistics.numberOfTransactions";
  static const String tootTipWarning = "contract.management.toolTipWarning";
  static const String toolTipWarning2 = "contract.management.toolTipWarning2";
  static const String collectionWallet = "wallet.types.collection";
  static const String handlingFeeMessage = "finance.handlingFeeMessage";
  static const String depositWallet = "wallet.types.deposit";
  static const String bonusWallet = "wallet.types.bonus";
  static const String rechargeQr = "wallet.recharge.qr";
  static const String rechargeAddress = "wallet.recharge.address";
  static const String copied = "common.actions.copied";
  static const String copy = "common.actions.copy";
  static const String trc20 = "wallet.recharge.types.trc20";
  static const String erc20 = "wallet.recharge.types.erc20";
  static const String thisRechargeAddressOneTime = "wallet.recharge.oneTimeAddress";
  static const String smallRechargesBelow = "wallet.recharge.smallRecharges";
  static const String pleaseComplete = "contract.investment.completeRecharge";
  static const String ok = "ok";
  static const String toCommunity = "toCommunity";
  static const String transferTo = "wallet.transfer.to";
  static const String usdt = "usdt";
  static const String toCollectionWallet = "wallet.transfer.toCollection";
  static const String toProfitWallet = "wallet.transfer.toProfit";
  static const String toDepositWallet = "wallet.transfer.toDeposit";
  static const String toCommunityWallet = "wallet.transfer.toCommunity";
  static const String notification = "common.navigation.notifications";
  static const String market = "market.title";
  static const String mainstreamCurrency = "market.mainstreamCurrency";
  static const String latestTransaction = "market.latestTransaction";
  static const String todaysChange = "market.todaysChange";
  static const String transactionRecords = "transactionRecords";
  static const String alertUnbind = "contract.management.unbindAlert";
  static const String alertCancel = "contract.management.cancelAlert";
  static const String unbind = "contract.actions.unbind";
  static const String canceled = "common.status.canceled";
  static const String weHaveSent = "auth.verification.weHaveSent";
  static const String enterVerification = "auth.verification.enterVerification";
  static const String emailVerification = "auth.verification.email";
  static const String didntRecieve = "auth.verification.didntReceive";
  static const String resendCode = "auth.verification.resendCode";
  static const String next = "common.actions.next";
  static const String confirm = "common.actions.confirm";
  static const String done = "common.actions.done";
  static const String termsAndConditions = "auth.signup.termsAndConditions";
  static const String tryAgain = "common.actions.tryAgain";
  static const String close = "common.actions.close";
  static const String chooseWallet = "wallet.choose";
  static const String setAmount = "setAmount";
  static const String enterAmount = "common.placeholders.amount";
  static const String walletPass = "walletPass";
  static const String summary = "summary";
  static const String purchaseContracts = "contract.purchase";
  static const String availableContracts = "contract.available";
  static const String enterPassword = "common.placeholders.password";
  static const String totalAmount = "common.labels.total";
  static const String numOfContracts = "contract.statistics.numberOfContracts";
  static const String buyIt = "contract.actions.buyIt";
  static const String returnRate = "contract.profit.returnRate";
  static const String retracementRate = "contract.statistics.retracementRate";
  static const String contractSize = "contract.details.size";
  static const String contractPrice = "contract.details.price";
  static const String contractType = "contract.type";
  static const String enterValidPassword = "enterValidPassword";
  static const String enterExactly6Digits = "auth.verification.enterExactly6Digits";
  static const String enterValidGoogleCode = "auth.verification.enterValidGoogleCode";
  static const String enterValidUsername = "enterValidUsername";
  static const String enterValidPhone = "enterValidPhone";
  static const String accountInformationStatus1 = "accountInformationStatus1";
  static const String accountInformationStatus2 = "accountInformationStatus2";
  static const String accountInformationStatus3 = "accountInformationStatus3";
  static const String accountInformationStatus4 = "accountInformationStatus4";
  static const String contractType1 = "contract.types.conservative";
  static const String contractType2 = "contract.types.robust";
  static const String contractType3 = "contract.types.radical";
  static const String invalidPassword = "invalidPassword";
  static const String otpCodeError = "auth.verification.otpCodeError";
  static const String enterEmailCode = "auth.verification.enterEmailCode";
  static const String invalidPassword2 = "invalidPassword2";
  static const String urlPrefix = "urlPrefix";
  static const String loginLinkSuffix = "loginLinkSuffix";
  static const String invitationCodeSuffix = "invitationCodeSuffix";
  static const String copiedClipboard = "common.actions.copiedClipboard";
  static const String enterThePassword = "enterThePassword";
  static const String actualAmount = "actualAmount";
  static const String withdrawalFee = "wallet.withdrawal.fee";
  static const String summaryWithdraw = "wallet.withdrawal.summary";
  static const String summaryTransfer = "wallet.transfer.summary";
  static const String purchased = "purchased";
  static const String successfully = "common.status.successfully";
  static const String walletNameExists = "wallet.address.nameExists";
  static const String walletAddressExists = "wallet.address.exists";
  static const String transferred = "transferred";
  static const String withdrawLimit = "wallet.withdrawal.limit";
  static const String insufficientBalance = "wallet.balance.insufficient";
  static const String emailError = "emailError";
  static const String uploadImageError = "uploadImageError";
  static const String frontImageLarge = "frontImageLarge";
  static const String backImageLarge = "backImageLarge";
  static const String addWithdrawal = "addWithdrawal";
  static const String deleteAddress = "wallet.address.delete";
  static const String confirmDelete = "common.actions.deleteConfirmation";
  static const String deleteAddressConfirmation = "wallet.address.deleteConfirmation";
  static const String zeroContractToast = "contract.management.zeroContractToast";
  static const String unBindSuccess = "contract.management.unBindSuccess";
  static const String cancelSuccess = "contract.management.cancelSuccess";
  static const String requestSuccess = "requestSuccess";
  static const String emailAlready = "emailAlready";
  static const String registerSuccess = "registerSuccess";
  static const String approvedSuccess = "approvedSuccess";
  static const String checkBoxSignupError = "auth.signup.checkboxError";
  static const String aadhaar = "auth.identity.types.aadhaar";
  static const String wallet = "wallet.title";
  static const String phone = "common.labels.phone";
  static const String google = "google";
  static const String typeGoogle = "typeGoogle";
  static const String typeConfirmWallet = "typeConfirmWallet";
  static const String signingYouOut = "auth.logout.signingOut";
  static const String or = "or";
  static const String hintWallet = "hintWallet";
  static const String hintPhone = "hintPhone";
  static const String statistics = "statistics";
  static const String profitRatio = "contract.profit.ratio";
  static const String theStatisticalSample = "theStatisticalSample";
  static const String buy = "contract.actions.buy";
  static const String pleaseWait = "pleaseWait";
  static const String typeCollection = "typeCollection";
  static const String typeDeposit = "typeDeposit";
  static const String numbering = "numbering";
  static const String purchaseTime = "contract.details.purchaseTime";
  static const String purchasePrice = "contract.details.purchasePrice";
  static const String earningsLastDay = "contract.profit.earningsLastDay";
  static const String pastEarnings = "contract.profit.past";
  static const String daysOfHolding = "contract.statistics.daysOfHolding";
  static const String daysSinceUnbind = "contract.statistics.daysSinceUnbind";
  static const String days = "common.time.days";
  static const String sellingTime = "contract.details.sellingTime";
  static const String sellingPrice = "contract.details.sellingPrice";
  static const String handlingFee = "finance.handlingFee";
  static const String volume = "contract.statistics.volume";
  static const String enterTheCode = "enterTheCode";
  static const String seconds = "common.time.seconds";
  static const String secondsUpper = "common.time.secondsUpper";
  static const String atLeast8character = "auth.password.atLeast8Chars";
  static const String atLeast8characterWithoutUpperCase =
      "auth.password.atLeast8CharsNoUpper";
  static const String transactionsHistory = "transactionsHistory";
  static const String addWithdrawalAddress = "wallet.address.addTitle";
  static const String datePrivacy = "auth.signup.effectiveDate";
  static const String dateTerms = "auth.signup.effectiveDate";
  static const String passwordHint = "passwordHint";
  static const String OR = "OR";
  static const String name = "common.labels.name";
  static const String type = "common.labels.type";
  static const String size = "common.labels.size";
  static const String address = "common.labels.address";
  static const String withdrawAddresses = "wallet.address.addresses";
  static const String somethingWentWrong = "common.messages.somethingWentWrong";
  static const String somethingWentWrongTryAgain = "common.messages.tryAgainLater";
  static const String sendCode = "auth.verification.sendCode";
  static const String passwordUpdatedSuccessfully =
      "auth.password.updated";
  static const String timesUpper = "timesUpper";
  static const String profitTimes = "contract.profit.times";
  static const String lossesTimes = "contract.profit.lossesTimes";
  static const String notAvailable = "notAvailable";
  static const String empty = "common.messages.empty";
  static const String profitWallet = "wallet.types.profit";

  // App Update Overlay
  static const String appUpdateAvailable = "app.update.available";
  static const String unknownVersion = "app.version.unknown";
  static const String updating = "updating";
  static const String readyToUpdate = "app.update.ready";
  static const String currentVersion = "app.version.current";
  static const String updateNow = "app.update.actions.updateNow";
  static const String downloading = "app.update.downloading";
  static const String installing = "app.update.installing";
  static const String downloadComplete = "common.messages.downloadComplete";
  static const String installationComplete = "common.messages.installationComplete";
  static const String downloadFailed = "common.messages.downloadFailed";
  static const String installationFailed = "common.messages.installationFailed";
  static const String retryUpdate = "app.update.actions.retry";
  static const String skipUpdate = "app.update.actions.skip";
  static const String forceUpdateRequired = "app.update.forceUpdateRequired";
  static const String downloadingUpdate = "app.update.downloading";
  static const String installingUpdate = "app.update.installing";
  static const String updateRequired = "app.update.required";
  static const String networkError = "common.messages.networkError";
  static const String storagePermissionRequired = "app.update.permissions.storage";
  static const String installPermissionRequired = "app.update.permissions.install";
  static const String downloadUrlNotAvailable = "common.messages.downloadUrlNotAvailable";
  static const String failedToDownloadUpdate = "app.update.failed.download";
  static const String failedToInstallUpdate = "app.update.failed.install";
  static const String communityWallet = "wallet.types.community";
  static const String walletPasswordShouldBe = "wallet.password.shouldBe6Chars";
  static const String googleCodeShouldBe = "auth.verification.googleCodeShouldBe";
  static const String enterValidErc = "wallet.recharge.enterValidErc";
  static const String enterValidTrc = "wallet.recharge.enterValidTrc";
  static const String maintenanceNotice = "app.maintenance";
  static const String systemNotifications = "systemNotifications";
  static const String contractAnnouncement = "contract.announcement";
  static const String noGalleryPermission = "noGalleryPermission";
  static const String changeIdentity = "auth.identity.change";
  static const String addAccountInfo = "addAccountInfo";
  static const String serviceAgreement = "serviceAgreement";
  static const String mentors = "community.mentors";
  static const String follow = "community.follow.title";
  static const String marketOverview = "market.overview";
  static const String winRate = "contract.profit.winRate";
  static const String monthly = "monthly";
  static const String drawdown = "contract.statistics.drawdown";
  static const String darkMode = "user.settings.darkMode";
  static const String usMarket = "market.usMarket";
  static const String stockTrading = "market.stockTrading";
  static const String todaysStockMarket = "market.todaysStockMarket";
  static const String hotStocks = "market.hotStocks";
  static const String leadingConcept = "market.leadingConcept";
  static const String leadingIndustry = "market.leadingIndustry";
  static const String gainers = "market.gainers";
  static const String losers = "market.losers";
  static const String change = "change";
  static const String latestPrice = "market.latestPrice";
  static const String search = "common.actions.search";
  static const String noResultsFound = "common.messages.noResults";
  static const String sell = "contract.actions.sell";
  static const String optional = "common.form.optional";
  static const String warning = "common.messages.warning";
  static const String high = "market.high";
  static const String low = "market.low";
  static const String open = "market.open";
  static const String smartInvestment = "contract.smart.title";
  static const String officeCompany = "user.profile.officeCompany";
  static const String workingAge = "user.profile.workingAge";
  static const String jobPosition = "user.profile.jobPosition";
  static const String expert = "community.expert";
  static const String portfolio = "finance.portfolio";
  static const String maxDrawdown = "contract.statistics.maxDrawdown";
  static const String oneClickPurchase = "contract.actions.oneClickPurchase";
  static const String automaticRenewal = "contract.management.automaticRenewal";
  static const String celebrityMentor = "community.celebrityMentor";
  static const String smartInvestmentCycle = "contract.smart.cycle";
  static const String tutorCommission = "finance.tutorCommission";
  static const String minimumPurchaseAmount = "contract.investment.minimum";
  static const String maximumPurchaseAmount = "contract.investment.maximum";
  static const String investmentUnits = "contract.investment.units";
  static const String followUpPurchaseAmount = "contract.smart.followUp.amount";
  static const String amountMustBeInUnits = "contract.investment.amountUnits";
  static const String smartInvestmentProducts = "contract.smart.products";
  static const String tradingDays = "contract.statistics.tradingDays";
  static const String haveReadAndAgreedToThe = "haveReadAndAgreedToThe";
  static const String intelligentFollowUpInvestment =
      "contract.smart.followUp.intelligent";
  static const String oneClickSmartInvestmentInstructions =
      "contract.smart.oneClick.title";
  static const String oneClickSmartInvestmentDescription =
      "contract.smart.oneClick.description";
  static const String transactionCycle = "transactionCycle";
  static const String fundingThreshold = "finance.fundingThreshold";
  static const String followPurchaseDetails = "contract.smart.followUp.details";
  static const String followPurchaseRecords = "contract.smart.followUp.records";
  static const String auditing = "auditing";
  static const String progress = "progress";
  static const String following = "community.follow.following";
  static const String inProcess = "inProcess";
  static const String finished = "common.status.finished";
  static const String rejected = "common.status.rejected";
  static const String completed = "common.status.completed";
  static const String readMore = "common.actions.readMore";
  static const String readLess = "common.actions.readLess";

  static const String typePaymentERC = "ERC20";
  static const String typePaymentTRC = "TRC20";
  static const String typeFront = "typeFront";
  static const String typeBack = "typeBack";

  static const String successful = "common.status.successful";
  static const String confirmInvestment = "contract.investment.confirm";
  static const String additionalInvestment = "contract.investment.additional";
  static const String withdrawInvestment = "wallet.withdrawal.investment";
  static const String paymentWallet = "wallet.types.payment";
  static const String noBalanceInformation = "wallet.balance.noBalanceInfo";
  static const String pleaseDepositSomeAmountToContinue =
      "contract.investment.depositToContinue";
  static const String noBalanceAvailable = "wallet.balance.noBalance";
  static const String willBeSentToProfitWallet = "willBeSentToProfitWallet";
  static const String withdrawal = "withdrawal";
  static const String purchase = "purchase";
  static const String years = "common.time.years";
  static const String orderNo = "contract.details.orderNo";
  static const String cycle = "contract.details.cycle";
  static const String mentorCommission = "finance.mentorCommission";
  static const String followUpPeriod = "contract.smart.followUp.period";
  static const String productName = "productName";
  static const String amount = "common.labels.amount";
  static const String starMentor = "community.starMentor";
  static const String reason = "reason";
  static const String day = "common.time.day";
  static const String append = "append";
  static const String details = "details";
  static const String followPurchaseDetailsTitle = "community.follow.followPurchaseDetails";
  static const String noDetailsAvailable = "noDetailsAvailable";
  static const String stockName = "market.stockName";
  static const String stockCode = "market.stockCode";
  static const String buyQuantity = "contract.details.buyQuantity";
  static const String buyingPrice = "contract.details.buyingPrice";
  static const String buyPosition = "contract.actions.buyPosition";
  static const String purchaseDate = "contract.details.purchaseDate";
  static const String sellQuantity = "contract.details.sellQuantity";
  static const String sellDate = "contract.details.sellDate";
  static const String revenueDetails = "finance.revenueDetails";
  static const String initialCapital = "finance.initialCapital";
  static const String additionalFunds = "finance.additionalFunds";
  static const String cumulativeIncome = "contract.profit.cumulativeIncome";
  static const String withdrawnProfit = "contract.profit.withdrawn";
  static const String appendRejectedAmount = "appendRejectedAmount";
  static const String totalRevenue = "finance.totalRevenue";
  static const String platformCommission = "finance.platformCommission";
  static const String revenue = "finance.revenue";
  static const String commission = "finance.commission";
  static const String additionalAmount = "finance.additionalAmount";
  static const String withdrawalAmount = "wallet.withdrawal.amount";
  static const String mustBeMultipleOf = "mustBeMultipleOf";
  static const String enterWithdrawalAmount = "wallet.withdrawal.enterAmount";
  static const String max = "max";
  static const String todayProfit = "contract.profit.today";
  static const String pleaseEnterAValidAmount = "contract.investment.validAmount";
  static const String minimumAmount = "minimumAmount";
  static const String maximumAmount = "maximumAmount";
  static const String minimumWithdrawalAmount = "wallet.withdrawal.minimum";
  static const String maximumWithdrawalAmount = "wallet.withdrawal.maximum";
  static const String amountMustBeMultipleOf = "contract.investment.amountMultiple";
  static const String amountExceedsMaximumAllowed =
      "contract.investment.amountExceedsMax";
  static const String invalidAmount = "contract.investment.invalidAmount";
  static const String insufficientAvailableBalance =
      "wallet.balance.insufficientAvailable";
  static const String invitationCodeError = "community.referral.invitationCodeError";
  static const String chat = "common.navigation.chat";
  static const String certificationCompleted = "auth.identity.certificationCompleted";
  static const String connecting = "connecting";
  static const String connected = "common.status.connected";
  static const String email = "common.labels.email";
  static const String phoneNumber = "common.labels.phone";
  static const String enterEmail = "common.placeholders.email";
  static const String otp = "auth.verification.otp";
  static const String otpPhone = "auth.verification.otpPhone";
  static const String sendCode1 = "auth.verification.sendCode";
  static const String in1 = "in1";
  static const String fundingWallet = "wallet.types.funding";
  static const String tradingWallet = "wallet.types.trading";
  static const String lockedBalance = "wallet.balance.locked";
  static const String preview = "common.actions.preview";
  static const String orderDate = "contract.details.orderDate";
  static const String investmentAmount = "contract.details.investmentAmount";
  static const String buyPrice = "contract.details.buyPrice";
  static const String sellPrice = "contract.details.sellPrice";
  static const String profit = "profit";
  static const String pending = "common.status.pending";
  static const String signinAgree = "auth.signup.agreement";
  static const String loginPasswordUpdatedToast = "auth.password.loginUpdatedToast";
  static const String buyingTimeNotice = "contract.details.buyingTimeNotice";
  static const String enterTransactionHash = "wallet.recharge.enterHash";
  static const String transactionHash = "transactionHash";
  static const String submitRecharge = "wallet.recharge.submit";
  static const String rechargeOrderSubmittedSuccessfully =
      "wallet.recharge.orderSubmitted";
  static const String enterValidTransactionHash = "wallet.recharge.enterValidHash";
  static const String depositTip = "wallet.recharge.tip";
  static const String finance = "common.navigation.finance";
  static const String expectedProfit = "contract.profit.expected";
  static const String actualProfit = "contract.profit.actual";
  static const String purchaseList = "purchaseList";
  static const String noPurchasesYet = "noPurchasesYet";
  static const String interestRate = "finance.interestRate";
  static const String lockPeriod = "contract.management.lockPeriod";
  static const String createTime = "contract.details.createTime";
  static const String profitAmount = "contract.profit.amount";
  static const String unknown = "unknown";
  static const String settled = "common.status.settled";
  static const String interestAmount = "finance.interestAmount";
  static const String sellTime = "contract.details.sellTime";
  static const String availableFrom = "availableFrom";
  static const String availableUntil = "availableUntil";
  static const String upcoming = "upcoming";
  static const String available = "common.status.available";
  static const String soldOut = "contract.status.soldOut";
  static const String directMembers = "community.directMembers";
  static const String totalMembers = "community.totalMembers";
  static const String otherMembers = "community.otherMembers";
  static const String firstGeneration = "firstGeneration";
  static const String secondGeneration = "secondGeneration";
  static const String thirdGeneration = "thirdGeneration";
  static const String failedToLoadCommissionStats =
      "failedToLoadCommissionStats";
  static const String retry = "common.actions.retry";
  static const String members = "community.members";
  static const String memberInfo = "user.profile.memberInfo";
  static const String reviewFailed = "reviewFailed";
  static const String up = "market.up";
  static const String flat = "market.flat";
  static const String down = "market.down";
  static const String pending2 = "pending2";

  static const String realtime = "realtime";
  static const String week = "common.time.week";
  static const String month = "common.time.month";
  static const String daily = "common.time.daily";
  static const String oneMin = "common.time.oneMin";
  static const String fiveMin = "common.time.fiveMin";
  static const String fifteenMin = "common.time.fifteenMin";
  static const String thirtyMin = "common.time.thirtyMin";
  static const String yearsOfExperience = "user.profile.yearsOfExperience";
  static const String monthlyReturn = "contract.profit.monthlyReturn";
  static const String endTime = "contract.details.endTime";
  static const String minimumAmountNotMeet = "minimumAmountNotMeet";
  static const String startTime = "contract.details.startTime";
  static const String noDataAvailable = "common.messages.noData";
  static const String benefitRules = "benefitRules";
  static const String selectWithdrawalAddress = "wallet.address.select";
  static const String withdrawHistory = "wallet.withdrawal.history";
  static const String noWithdrawHistory = "noWithdrawHistory";
  static const String withdrawalStatus = "withdrawalStatus";
  static const String processing = "common.status.processing";
  static const String completeTime = "completeTime";
  static const String all = "all";
  static const String unprocessed = "unprocessed";
  static const String success = "common.status.success";
  static const String pleaseEnterAValidInvestmentAmount =
      "contract.investment.validInvestmentAmount";
  static const String pleaseSelectAnInvestmentProduct =
      "contract.investment.selectProduct";
  static const String pleaseAcceptTheServiceAgreement =
      "pleaseAcceptTheServiceAgreement";
  static const String vipLevelError = "community.vip.levelError";

  static const String whatIsATransactionHash = "wallet.recharge.whatIsHash";
  static const String whatIsATransactionHashDescription =
      "wallet.recharge.hashDescription";
  static const String erc20_title = "wallet.recharge.erc20.title";
  static const String trc20_title = "wallet.recharge.trc20.title";
  static const String erc20_description = "wallet.recharge.erc20.description";
  static const String trc20_description = "wallet.recharge.trc20.description";
  static const String transactionHashDescription = "wallet.recharge.hashDescription";
  static const String transactionHashDescription2 = "wallet.recharge.hashDescription2";
  static const String vipNotice = "community.vip.notice";
  static const String vipNoticeDescription = "community.vip.description";
  static const String aMonthAgo = "common.time.aMonthAgo";
  static const String daysAgo = "common.time.daysAgo";
  static const String anHourAgo = "common.time.anHourAgo";
  static const String aMinuteAgo = "aMinuteAgo";
  static const String justNow = "common.time.justNow";
  static const String aDayAgo = "common.time.aDayAgo";
  static const String hoursAgo = "common.time.hoursAgo";
  static const String minutesAgo = "minutesAgo";
  static const String singleAmount = "singleAmount";
  static const String iHaveReadAndAgreed = "iHaveReadAndAgreed";
  static const String unavailableFunds = "finance.unavailableFunds";
  static const String availableFunds = "finance.availableFunds";
}
