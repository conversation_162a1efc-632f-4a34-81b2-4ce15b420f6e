# SuperFuture Trading App

A comprehensive Flutter trading application built with clean architecture principles, integrating with the SuperFuture API for real-time market data, trading operations, and wallet management.

## Features

### 🔐 Authentication & Security
- User login/registration with email verification
- Google Authenticator 2FA support
- Identity verification with document upload
- Secure password management
- Biometric authentication support

### 📈 Market Data
- Real-time stock quotes and market data
- Interactive K-line charts with multiple timeframes
- Market depth and order book visualization
- Stock search and watchlist management
- Market indices and sector performance
- Gain/loss distribution analytics

### 💼 Trading System
- Product-based trading with mentor following
- Order management (buy/sell/modify/cancel)
- Position tracking and P&L monitoring
- Transaction history and reporting
- Risk management tools
- Copy trading functionality

### 💰 Wallet Management
- Multi-wallet system (Main, Trading, Savings)
- Cryptocurrency deposits and withdrawals
- Internal transfers between wallets
- Transaction history and statements
- QR code generation for deposits
- Address management for withdrawals

### 👥 Community Features
- Friends list and referral system
- Mentor profiles and performance tracking
- Social trading features
- Announcements and news feed
- Customer support integration

### ⚙️ Configuration
- App settings and preferences
- Notification management
- Privacy and security settings
- Terms of service and privacy policy
- About and help sections

## Architecture

The app follows a **feature-first, clean architecture** approach with clear separation of concerns:

```
lib/
├── core/                  # Core utilities and infrastructure
│   ├── constants/         # API endpoints and app constants
│   ├── di/               # Dependency injection setup
│   ├── network/          # Network layer (Dio, ResponseResult)
│   ├── storage/          # Secure storage helpers
│   ├── utils/            # Utility classes and helpers
│   └── websocket/        # WebSocket service for real-time data
├── config/               # App configuration
│   ├── app_config.dart   # App-wide configuration
│   ├── app_router.dart   # Navigation and routing
│   └── app_theme.dart    # UI theme and styling
├── features/             # Feature modules
│   ├── auth/             # Authentication feature
│   ├── market/           # Market data feature
│   ├── trading/          # Trading operations feature
│   ├── wallet/           # Wallet management feature
│   ├── profile/          # User profile feature
│   └── community/        # Community features
└── shared/               # Shared components
    ├── screens/          # Common screens (splash, main)
    └── widgets/          # Reusable UI components
```

### Feature Structure

Each feature follows this consistent structure:

```
feature_name/
├── domain/               # Domain layer (business logic)
│   ├── models/           # Data models with Freezed
│   ├── repository/       # Repository interfaces
│   └── services/         # Service implementations
├── logic/                # State management (Cubits)
├── screens/              # UI screens
└── widgets/              # Feature-specific UI components
```

## Tech Stack

### Core Framework
- **Flutter 3.7.2+** - Cross-platform mobile development
- **Dart** - Programming language

### State Management
- **flutter_bloc** - BLoC pattern implementation
- **equatable** - Value equality for state objects
- **freezed** - Immutable data classes and unions

### Network & API
- **dio** - HTTP client for API calls
- **retrofit** - Type-safe HTTP client generator
- **json_annotation** - JSON serialization
- **pretty_dio_logger** - Network request logging

### Dependency Injection
- **get_it** - Service locator
- **injectable** - Code generation for dependency injection

### Local Storage
- **shared_preferences** - Simple key-value storage
- **flutter_secure_storage** - Encrypted storage for sensitive data

### UI Components
- **cached_network_image** - Efficient image loading and caching
- **fl_chart** - Beautiful charts for market data
- **qr_flutter** - QR code generation

### Navigation
- **go_router** - Declarative routing

### Real-time Communication
- **web_socket_channel** - WebSocket support for live data

### Security
- **otp** - One-time password generation for 2FA

### Development Tools
- **build_runner** - Code generation
- **json_serializable** - JSON serialization code generation
- **retrofit_generator** - Retrofit code generation
- **injectable_generator** - Dependency injection code generation
- **freezed** - Data class code generation
- **flutter_lints** - Dart linting rules

## Getting Started

### Prerequisites

- Flutter SDK 3.7.2 or higher
- Dart SDK 3.0.0 or higher
- Android Studio / VS Code with Flutter extensions
- iOS development: Xcode (for iOS builds)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd sf_v2
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Generate code**
   ```bash
   dart run build_runner build
   ```

4. **Run the app**
   ```bash
   flutter run
   ```

### Code Generation

The project uses code generation for various purposes. Run this command whenever you modify:
- Model classes with `@freezed` annotation
- Repository interfaces with `@injectable` annotation
- JSON serializable classes

```bash
dart run build_runner build --delete-conflicting-outputs
```

### Environment Setup

The app connects to the SuperFuture API at `https://www.superfuture.world/api/v2`.

For development, you can modify the base URL in `lib/core/constants/api_endpoints.dart`.

## Project Structure Details

### Core Layer

#### Network Layer
- **NetworkProvider**: Centralized HTTP client with authentication handling
- **ResponseResult**: Generic wrapper for API responses with error handling
- **API Endpoints**: Centralized endpoint definitions

#### Storage Layer
- **SecureStorageHelper**: Encrypted storage for sensitive data (tokens, keys)
- **AuthUtils**: Authentication state management and token handling

#### WebSocket Layer
- **WebSocketService**: Real-time data connection with auto-reconnection

### Feature Implementation

#### Authentication Flow
1. User enters credentials on login screen
2. AuthCubit handles login logic via AuthRepository
3. Successful login stores token securely
4. App navigates to main screen with authenticated state

#### Market Data Flow
1. MarketScreen requests data via MarketCubit
2. MarketRepository fetches data from API
3. Real-time updates via WebSocket subscription
4. UI updates automatically via BLoC state changes

#### Trading Flow
1. User selects product/mentor to follow
2. TradingCubit handles order placement
3. Real-time order updates via WebSocket
4. Position tracking and P&L calculation

#### Wallet Flow
1. Multiple wallet types with separate balances
2. Secure transaction processing
3. QR code generation for deposits
4. Address management for withdrawals

## API Integration

The app integrates with the SuperFuture API v2, providing:

- **Authentication endpoints** for login, registration, 2FA
- **Market data endpoints** for stocks, indices, K-line data
- **Trading endpoints** for orders, positions, transactions
- **Wallet endpoints** for balances, transfers, history
- **User management endpoints** for profiles, verification
- **Configuration endpoints** for app settings

### Authentication

All authenticated requests include the Bearer token in the Authorization header:
```
Authorization: Bearer <token>
```

### Error Handling

The app implements comprehensive error handling:
- Network errors with retry mechanisms
- API errors with user-friendly messages
- Authentication errors with automatic logout
- Validation errors with form feedback

## Testing

### Running Tests

```bash
# Run all tests
flutter test

# Run tests with coverage
flutter test --coverage

# Run integration tests
flutter drive --target=test_driver/app.dart
```

### Test Structure

```
test/
├── unit/                 # Unit tests for business logic
├── widget/               # Widget tests for UI components
└── integration/          # Integration tests for user flows
```

## Building for Production

### Android

```bash
# Build APK
flutter build apk --release

# Build App Bundle
flutter build appbundle --release
```

### iOS

```bash
# Build for iOS
flutter build ios --release
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Code Style

- Follow Dart/Flutter style guidelines
- Use meaningful variable and function names
- Add comments for complex business logic
- Ensure all tests pass before submitting PR

### Commit Messages

Use conventional commit format:
- `feat:` for new features
- `fix:` for bug fixes
- `docs:` for documentation changes
- `style:` for formatting changes
- `refactor:` for code refactoring
- `test:` for adding tests

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation in the `/docs` folder

## Changelog

See [CHANGELOG.md](CHANGELOG.md) for a detailed list of changes and version history.
